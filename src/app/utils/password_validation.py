import os
import re
from typing import List


class PasswordValidationError(ValueError):
    """Custom exception for password validation failures."""

    def __init__(self, errors: List[str]):
        self.errors = errors
        super().__init__(f"Password validation failed: {', '.join(errors)}")


class PasswordValidator:
    """
    Comprehensive password validator with configurable security rules.
    """

    def __init__(self):
        # Load configuration from environment variables with sensible defaults
        self.min_length = int(os.getenv("MIN_PASSWORD_LENGTH", "8"))
        self.require_uppercase = (
            os.getenv("REQUIRE_UPPERCASE", "true").lower() == "true"
        )
        self.require_lowercase = (
            os.getenv("REQUIRE_LOWERCASE", "true").lower() == "true"
        )
        self.require_numbers = os.getenv("REQUIRE_NUMBERS", "true").lower() == "true"
        self.require_special_chars = (
            os.getenv("REQUIRE_SPECIAL_CHARS", "true").lower() == "true"
        )

        # Common weak passwords to reject
        self.common_passwords = {
            "password",
            "123456",
            "password123",
            "admin",
            "qwerty",
            "letmein",
            "welcome",
            "monkey",
            "1234567890",
            "abc123",
            "password1",
            "iloveyou",
            "trustno1",
            "dragon",
            "master",
        }

        # Sequential patterns to avoid
        self.sequential_patterns = ["123456", "abcdef", "qwerty", "asdfgh", "zxcvbn"]

    def validate_password(self, password: str) -> bool:
        """
        Validate password against all security rules.

        Args:
            password: The password to validate

        Returns:
            True if password is valid

        Raises:
            PasswordValidationError: If password fails validation
        """
        errors = []

        # Check minimum length
        if len(password) < self.min_length:
            errors.append(
                f"Password must be at least {self.min_length} characters long"
            )

        # Check maximum length (prevent DoS attacks)
        if len(password) > 128:
            errors.append("Password must not exceed 128 characters")

        # Check for uppercase letters
        if self.require_uppercase and not re.search(r"[A-Z]", password):
            errors.append("Password must contain at least one uppercase letter")

        # Check for lowercase letters
        if self.require_lowercase and not re.search(r"[a-z]", password):
            errors.append("Password must contain at least one lowercase letter")

        # Check for numbers
        if self.require_numbers and not re.search(r"\d", password):
            errors.append("Password must contain at least one number")

        # Check for special characters
        if self.require_special_chars and not re.search(
            r'[!@#$%^&*(),.?":{}|<>]', password
        ):
            errors.append(
                'Password must contain at least one special character (!@#$%^&*(),.?":{}|<>)'
            )

        # Check against common passwords
        if password.lower() in self.common_passwords:
            errors.append("Password is too common and easily guessable")

        # Check for sequential patterns
        password_lower = password.lower()
        for pattern in self.sequential_patterns:
            if pattern in password_lower:
                errors.append(
                    "Password contains sequential characters that are easily guessable"
                )
                break

        # Check for repeated characters (more than 3 in a row)
        if re.search(r"(.)\1{3,}", password):
            errors.append("Password contains too many repeated characters")

        # Check if password is just numbers
        if password.isdigit():
            errors.append("Password cannot be only numbers")

        # Check if password is just letters
        if password.isalpha():
            errors.append("Password cannot be only letters")

        if errors:
            raise PasswordValidationError(errors)

        return True

    def get_password_strength_score(self, password: str) -> int:
        """
        Calculate password strength score (0-100).

        Args:
            password: The password to score

        Returns:
            Score from 0 (weakest) to 100 (strongest)
        """
        score = 0

        # Length scoring
        if len(password) >= 8:
            score += 20
        if len(password) >= 12:
            score += 10
        if len(password) >= 16:
            score += 10

        # Character diversity
        if re.search(r"[a-z]", password):
            score += 10
        if re.search(r"[A-Z]", password):
            score += 10
        if re.search(r"\d", password):
            score += 10
        if re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            score += 15

        # Additional complexity
        unique_chars = len(set(password))
        if unique_chars >= len(password) * 0.7:  # 70% unique characters
            score += 10

        # Penalty for common patterns
        if password.lower() in self.common_passwords:
            score -= 30

        for pattern in self.sequential_patterns:
            if pattern in password.lower():
                score -= 20
                break

        return max(0, min(100, score))

    def suggest_improvements(self, password: str) -> List[str]:
        """
        Suggest improvements for a weak password.

        Args:
            password: The password to analyze

        Returns:
            List of improvement suggestions
        """
        suggestions = []

        if len(password) < self.min_length:
            suggestions.append(
                f"Increase length to at least {self.min_length} characters"
            )

        if not re.search(r"[A-Z]", password):
            suggestions.append("Add uppercase letters")

        if not re.search(r"[a-z]", password):
            suggestions.append("Add lowercase letters")

        if not re.search(r"\d", password):
            suggestions.append("Add numbers")

        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            suggestions.append("Add special characters")

        if password.lower() in self.common_passwords:
            suggestions.append("Avoid common passwords")

        return suggestions


# Global password validator instance
password_validator = PasswordValidator()


def validate_password_strength(password: str) -> str:
    """
    Pydantic-compatible password validator function.

    Args:
        password: The password to validate

    Returns:
        The validated password

    Raises:
        ValueError: If password doesn't meet requirements
    """
    try:
        password_validator.validate_password(password)
        return password
    except PasswordValidationError as e:
        raise ValueError(str(e))
