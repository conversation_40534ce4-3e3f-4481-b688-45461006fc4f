from typing import Any, Dict

from utils.celery_worker import celery_app
from utils.logger import get_service_logger

logger = get_service_logger("sample_task")


@celery_app.task(
    name="jobs.sample.sample_job",
    bind=True,
    max_retries=3,
    default_retry_delay=60,
)
def sample_job(
    self,
) -> Dict[str, Any]:
    task_id = self.request.id
    try:
        # Simulate a task processing
        import time

        time.sleep(10)  # Simulate a long-running task
        return {"status": "success", "task_id": task_id}

    except Exception as e:
        logger.exception(f"[IMPORT] Unexpected error in task {task_id}: {e}")
        return {
            "status": "error",
            "task_id": task_id,
            "error_type": "unexpected_error",
            "error_message": str(e),
        }
