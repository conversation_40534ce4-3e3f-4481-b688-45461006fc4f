from typing import List

from pydantic import BaseModel


class StateResponse(BaseModel):
    id: int
    name: str

    class Config:
        from_attributes = True


class MarketTypeResponse(BaseModel):
    id: int
    market_type: str

    class Config:
        from_attributes = True


class RenewableEnergyTypeResponse(BaseModel):
    id: int
    renewable_energy_type: str

    class Config:
        from_attributes = True


class SettlementTypeResponse(BaseModel):
    id: int
    settlement_type: str

    class Config:
        from_attributes = True


class ConditionResponse(BaseModel):
    id: int
    condition: str

    class Config:
        from_attributes = True


class RatioValueResponse(BaseModel):
    id: int
    ratio_value: str

    class Config:
        from_attributes = True


class FactorResponse(BaseModel):
    id: int
    factor: str

    class Config:
        from_attributes = True


class CarryForwardResponse(BaseModel):
    id: int
    carry_forward: str

    class Config:
        from_attributes = True


# Simplified response models that return {key: [values]} format
class StatesListResponse(BaseModel):
    states: List[StateResponse]


class MarketTypesListResponse(BaseModel):
    market_types: List[MarketTypeResponse]


class RenewableEnergyTypesListResponse(BaseModel):
    renewable_energy_types: List[RenewableEnergyTypeResponse]


class SettlementTypesListResponse(BaseModel):
    settlement_types: List[SettlementTypeResponse]


class ConditionsListResponse(BaseModel):
    conditions: List[ConditionResponse]


class RatioValuesListResponse(BaseModel):
    ratio_values: List[RatioValueResponse]


class FactorsListResponse(BaseModel):
    factors: List[FactorResponse]


class CarryForwardsListResponse(BaseModel):
    carry_forwards: List[CarryForwardResponse]
