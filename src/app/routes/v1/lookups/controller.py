from database.core import DbSession
from fastapi import APIRouter, HTTPException, status
from utils.logger import get_controller_logger

from ..auth.service import CurrentUser
from . import models, service

logger = get_controller_logger("lookups_controller")
router = APIRouter(prefix="/v1/lookups", tags=["Lookups"])


@router.get("/states", response_model=models.StatesListResponse)
def get_states(
    db: DbSession,
    current_user: CurrentUser,
):
    """Get all states"""
    try:
        states = service.get_all_states(db)
        return models.StatesListResponse(states=states)
    except Exception as e:
        logger.error(f"Error retrieving states: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving states",
        )


@router.get("/market_types", response_model=models.MarketTypesListResponse)
def get_market_types(
    db: DbSession,
    current_user: CurrentUser,
):
    """Get all distinct market types"""
    try:
        market_types = service.get_all_market_types(db)
        return models.MarketTypesListResponse(market_types=market_types)
    except Exception as e:
        logger.error(f"Error retrieving market types: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving market types",
        )


@router.get(
    "/renewable_energy_types", response_model=models.RenewableEnergyTypesListResponse
)
def get_renewable_energy_types(
    db: DbSession,
    current_user: CurrentUser,
):
    """Get all distinct renewable energy types"""
    try:
        re_types = service.get_all_renewable_energy_types(db)
        return models.RenewableEnergyTypesListResponse(renewable_energy_types=re_types)
    except Exception as e:
        logger.error(f"Error retrieving renewable energy types: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving renewable energy types",
        )


@router.get("/settlement_types", response_model=models.SettlementTypesListResponse)
def get_settlement_types(
    db: DbSession,
    current_user: CurrentUser,
):
    """Get all distinct settlement types"""
    try:
        settlement_types = service.get_all_settlement_types(db)
        return models.SettlementTypesListResponse(settlement_types=settlement_types)
    except Exception as e:
        logger.error(f"Error retrieving settlement types: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving settlement types",
        )


@router.get("/conditions", response_model=models.ConditionsListResponse)
def get_conditions(
    db: DbSession,
    current_user: CurrentUser,
):
    """Get all distinct conditions"""
    try:
        conditions = service.get_all_conditions(db)
        return models.ConditionsListResponse(conditions=conditions)
    except Exception as e:
        logger.error(f"Error retrieving conditions: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving conditions",
        )


@router.get("/ratio_values", response_model=models.RatioValuesListResponse)
def get_ratio_values(
    db: DbSession,
    current_user: CurrentUser,
):
    """Get all distinct ratio values"""
    try:
        ratio_values = service.get_all_ratio_values(db)
        return models.RatioValuesListResponse(ratio_values=ratio_values)
    except Exception as e:
        logger.error(f"Error retrieving ratio values: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving ratio values",
        )


@router.get("/factors", response_model=models.FactorsListResponse)
def get_factors(
    db: DbSession,
    current_user: CurrentUser,
):
    """Get all distinct factors"""
    try:
        factors = service.get_all_factors(db)
        return models.FactorsListResponse(factors=factors)
    except Exception as e:
        logger.error(f"Error retrieving factors: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving factors",
        )


@router.get("/carry_forwards", response_model=models.CarryForwardsListResponse)
def get_carry_forwards(
    db: DbSession,
    current_user: CurrentUser,
):
    """Get all distinct carry forward values"""
    try:
        carry_forwards = service.get_all_carry_forwards(db)
        return models.CarryForwardsListResponse(carry_forwards=carry_forwards)
    except Exception as e:
        logger.error(f"Error retrieving carry forwards: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving carry forwards",
        )
