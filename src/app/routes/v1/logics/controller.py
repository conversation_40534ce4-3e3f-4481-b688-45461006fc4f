from uuid import UUID

from database.core import DbSession
from fastapi import APIRouter, HTTPException, status
from utils.logger import get_controller_logger

from ..auth.service import CurrentUser
from . import models, service

logger = get_controller_logger("logics_controller")
router = APIRouter(prefix="/v1/logics", tags=["Logics"])


@router.post("/", response_model=models.LogicCreateResponse)
def create_logic(
    logic_create: models.LogicCreateRequest,
    db: DbSession,
    current_user: CurrentUser,
):
    """Create a new logic (starts as draft)"""
    try:
        return service.create_logic(db, current_user, logic_create)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating logic: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error creating logic",
        )


@router.patch("/{logic_id}", response_model=models.LogicUpdateResponse)
def update_logic(
    logic_id: UUID,
    logic_update: models.LogicUpdateRequest,
    db: DbSession,
    current_user: CurrentUser,
):
    """Update a logic by ID (can be used for autosave from UI, updating values, or changing draft status)"""
    try:
        return service.update_logic(db, current_user, logic_id, logic_update)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating logic {logic_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error updating logic",
        )


@router.delete("/{logic_id}", response_model=models.DeleteLogicResponse)
def delete_logic(
    logic_id: UUID,
    db: DbSession,
    current_user: CurrentUser,
):
    """Delete a logic by ID (soft delete)"""
    try:
        return service.delete_logic(db, current_user, logic_id)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting logic {logic_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error deleting logic",
        )
