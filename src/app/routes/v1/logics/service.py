from uuid import UUID

from database.core import DbSession
from db_services import logics as logics_db_services
from fastapi import HTTPException, status
from routes.v1.auth.service import CurrentUser
from utils.logger import get_service_logger

from . import models

logger = get_service_logger("logics_service")


def delete_logic(
    db: DbSession,
    current_user: CurrentUser,
    logic_id: UUID,
) -> models.DeleteLogicResponse:
    """
    Delete a logic by ID (soft delete)
    """
    try:
        # Check if logic exists and is not already deleted
        logic = logics_db_services.get_logic_by_id_query(db, logic_id)

        if not logic:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Logic not found"
            )

        # Perform soft delete
        logics_db_services.delete_logic_query(db, logic)

        logger.info(f"Logic {logic_id} deleted successfully by user {current_user.id}")

        return models.DeleteLogicResponse(
            message="Logic deleted successfully", logic_id=logic_id
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting logic {logic_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error deleting logic",
        )
