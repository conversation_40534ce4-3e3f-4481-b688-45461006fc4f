from typing import Any, Dict
from uuid import UUID

from database.core import DbSession
from db_services import logics as logics_db_services
from entities.backward_testing import BackwardTesting
from entities.conditions import Condition
from entities.customer_and_state import CustomerAndState
from entities.demand import Demand
from entities.deployments import Deployment
from entities.forward_testing import ForwardTesting
from entities.list_of_losses import (
    InterStateTransmissionLoss,
    IntraStateTransmissionLoss,
    WheelingLoss,
)
from entities.logics import Logic
from entities.market_related_data import MarketRelatedData
from entities.open_access_charge_general_information import (
    AdditionalSurcharge,
    CrossSubsidySurcharge,
    DSMCharge,
    IEXTransactionFee,
    InterstateTransmissionCharge,
    IntrastateTransmissionCharge,
    RECCharge,
    SchedulingAndOtherCharge,
    WheelingCharge,
)
from entities.open_access_charges_tod import (
    TodBaseDiscomTariff,
    TodFuelSurcharge,
    TodRebate,
    TodTimeBlock,
    TodTotalDiscomLandedTariff,
)
from entities.settlement import Settlement
from entities.solar_and_other_re import SolarAndOtherRe
from fastapi import HTTPException, status
from routes.v1.auth.service import CurrentUser
from utils.logger import get_service_logger

from . import models

logger = get_service_logger("logics_service")


def delete_logic(
    db: DbSession,
    current_user: CurrentUser,
    logic_id: UUID,
) -> models.DeleteLogicResponse:
    """
    Delete a logic by ID (soft delete)
    """
    try:
        # Check if logic exists and is not already deleted
        logic = logics_db_services.get_logic_by_id_query(db, logic_id)

        if not logic:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Logic not found"
            )

        # Perform soft delete
        logics_db_services.delete_logic_query(db, logic)

        logger.info(f"Logic {logic_id} deleted successfully by user {current_user.id}")

        return models.DeleteLogicResponse(
            message="Logic deleted successfully", logic_id=logic_id
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting logic {logic_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error deleting logic",
        )


def create_logic(
    db: DbSession,
    current_user: CurrentUser,
    logic_create: models.LogicCreateRequest,
) -> models.LogicCreateResponse:
    """
    Create a new logic with all related entities
    """
    try:
        # Validate customers exist if customer_ids are provided
        validated_customers = []
        if logic_create.customer_ids:
            validated_customers = logics_db_services.get_customers_by_ids_query(
                db, logic_create.customer_ids
            )
            if len(validated_customers) != len(logic_create.customer_ids):
                found_ids = [c.id for c in validated_customers]
                missing_ids = [
                    cid for cid in logic_create.customer_ids if cid not in found_ids
                ]
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Customers with IDs {missing_ids} not found",
                )

        # Create logic object (starts as draft by default)
        new_logic = Logic(
            created_by=current_user.user_id,
            updated_by=current_user.user_id,
            customers=validated_customers,
        )

        # Create the logic first to get the ID
        created_logic = logics_db_services.create_logic_query(db, new_logic)

        # Create related entities
        _create_related_entities(db, created_logic.id, logic_create)

        # Commit all changes
        db.commit()

        # Refresh to get all relationships
        db.refresh(created_logic)

        logger.info(
            f"Logic {created_logic.id} created successfully by user {current_user.user_id}"
        )

        return models.LogicCreateResponse(
            message="Logic created successfully",
            logic=models.LogicResponse.model_validate(created_logic),
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating logic: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error creating logic",
        )


def _create_related_entities(
    db: DbSession, logic_id: UUID, logic_data: models.LogicCreateRequest
) -> None:
    """
    Helper function to create all related entities for a logic
    """
    # Create demand
    if logic_data.demand:
        demand_dict = logic_data.demand.model_dump(exclude_unset=True)
        if demand_dict:  # Only create if there's actual data
            demand = Demand(logic_id=logic_id, **demand_dict)
            db.add(demand)

    # Create market related data
    if logic_data.market_related_data:
        for mrd_data in logic_data.market_related_data:
            mrd_dict = mrd_data.model_dump(exclude_unset=True)
            if mrd_dict:
                mrd = MarketRelatedData(logic_id=logic_id, **mrd_dict)
                db.add(mrd)

    # Create solar and other RE
    if logic_data.solar_and_other_re:
        for solar_data in logic_data.solar_and_other_re:
            solar_dict = solar_data.model_dump(exclude_unset=True)
            if solar_dict:
                solar = SolarAndOtherRe(logic_id=logic_id, **solar_dict)
                db.add(solar)

    # Create charge entities
    _create_charge_entities_helper(db, logic_id, logic_data)

    # Create TOD entities
    _create_tod_entities_helper(db, logic_id, logic_data)

    # Create loss entities
    _create_loss_entities_helper(db, logic_id, logic_data)

    # Create other entities
    _create_other_entities_helper(db, logic_id, logic_data)


def _create_charge_entities_helper(
    db: DbSession, logic_id: UUID, logic_data: models.LogicCreateRequest
) -> None:
    """Helper to create all charge-type entities"""
    charge_mappings = [
        (logic_data.interstate_transmission_charges, InterstateTransmissionCharge),
        (logic_data.intrastate_transmission_charges, IntrastateTransmissionCharge),
        (logic_data.wheeling_charges, WheelingCharge),
        (logic_data.cross_subsidy_surcharges, CrossSubsidySurcharge),
        (logic_data.additional_surcharges, AdditionalSurcharge),
        (logic_data.scheduling_and_other_charges, SchedulingAndOtherCharge),
        (logic_data.iex_transaction_fees, IEXTransactionFee),
        (logic_data.rec_charges, RECCharge),
        (logic_data.dsm_charges, DSMCharge),
    ]

    for charge_data_list, entity_class in charge_mappings:
        if charge_data_list:
            for charge_data in charge_data_list:
                charge_dict = charge_data.model_dump(exclude_unset=True)
                if charge_dict:
                    entity = entity_class(logic_id=logic_id, **charge_dict)
                    db.add(entity)


def _create_tod_entities_helper(
    db: DbSession, logic_id: UUID, logic_data: models.LogicCreateRequest
) -> None:
    """Helper to create all TOD-type entities"""
    # TOD Time Blocks
    if logic_data.tod_time_blocks:
        for tod_data in logic_data.tod_time_blocks:
            tod_dict = tod_data.model_dump(exclude_unset=True)
            if tod_dict:
                tod = TodTimeBlock(logic_id=logic_id, **tod_dict)
                db.add(tod)

    # TOD Base Discom Tariffs
    if logic_data.tod_base_discom_tariffs:
        for tariff_data in logic_data.tod_base_discom_tariffs:
            tariff_dict = tariff_data.model_dump(exclude_unset=True)
            if tariff_dict:
                tariff = TodBaseDiscomTariff(logic_id=logic_id, **tariff_dict)
                db.add(tariff)

    # TOD Rebates
    if logic_data.tod_rebates:
        for rebate_data in logic_data.tod_rebates:
            rebate_dict = rebate_data.model_dump(exclude_unset=True)
            if rebate_dict:
                rebate = TodRebate(logic_id=logic_id, **rebate_dict)
                db.add(rebate)

    # TOD Fuel Surcharges
    if logic_data.tod_fuel_surcharges:
        for fuel_data in logic_data.tod_fuel_surcharges:
            fuel_dict = fuel_data.model_dump(exclude_unset=True)
            if fuel_dict:
                fuel = TodFuelSurcharge(logic_id=logic_id, **fuel_dict)
                db.add(fuel)

    # TOD Total Discom Landed Tariffs
    if logic_data.tod_total_discom_landed_tariffs:
        for total_data in logic_data.tod_total_discom_landed_tariffs:
            total_dict = total_data.model_dump(exclude_unset=True)
            if total_dict:
                total = TodTotalDiscomLandedTariff(logic_id=logic_id, **total_dict)
                db.add(total)


def _create_loss_entities_helper(
    db: DbSession, logic_id: UUID, logic_data: models.LogicCreateRequest
) -> None:
    """Helper to create all loss-type entities"""
    loss_mappings = [
        (logic_data.inter_state_transmission_losses, InterStateTransmissionLoss),
        (logic_data.intra_state_transmission_losses, IntraStateTransmissionLoss),
        (logic_data.wheeling_losses, WheelingLoss),
    ]

    for loss_data_list, entity_class in loss_mappings:
        if loss_data_list:
            for loss_data in loss_data_list:
                loss_dict = loss_data.model_dump(exclude_unset=True)
                if loss_dict:
                    entity = entity_class(logic_id=logic_id, **loss_dict)
                    db.add(entity)


def _create_other_entities_helper(
    db: DbSession, logic_id: UUID, logic_data: models.LogicCreateRequest
) -> None:
    """Helper to create other miscellaneous entities"""
    # Customer and State
    if logic_data.customer_and_state:
        for cs_data in logic_data.customer_and_state:
            cs_dict = cs_data.model_dump(exclude_unset=True)
            if cs_dict:
                cs = CustomerAndState(logic_id=logic_id, **cs_dict)
                db.add(cs)

    # Settlements
    if logic_data.settlements:
        for settlement_data in logic_data.settlements:
            settlement_dict = settlement_data.model_dump(exclude_unset=True)
            if settlement_dict:
                settlement = Settlement(logic_id=logic_id, **settlement_dict)
                db.add(settlement)

    # Conditions
    if logic_data.conditions:
        for condition_data in logic_data.conditions:
            condition_dict = condition_data.model_dump(exclude_unset=True)
            if condition_dict:
                condition = Condition(logic_id=logic_id, **condition_dict)
                db.add(condition)

    # Deployments
    if logic_data.deployments:
        for deployment_data in logic_data.deployments:
            deployment_dict = deployment_data.model_dump(exclude_unset=True)
            if deployment_dict:
                deployment = Deployment(logic_id=logic_id, **deployment_dict)
                db.add(deployment)

    # Forward Testings
    if logic_data.forward_testings:
        for ft_data in logic_data.forward_testings:
            ft_dict = ft_data.model_dump(exclude_unset=True)
            if ft_dict:
                ft = ForwardTesting(logic_id=logic_id, **ft_dict)
                db.add(ft)

    # Backward Testings
    if logic_data.backward_testings:
        for bt_data in logic_data.backward_testings:
            bt_dict = bt_data.model_dump(exclude_unset=True)
            if bt_dict:
                bt = BackwardTesting(logic_id=logic_id, **bt_dict)
                db.add(bt)


def update_logic(
    db: DbSession,
    current_user: CurrentUser,
    logic_id: UUID,
    logic_update: models.LogicUpdateRequest,
) -> models.LogicUpdateResponse:
    """
    Update an existing logic with partial data including all related entities
    """
    try:
        # Get existing logic
        existing_logic = logics_db_services.get_logic_by_id_query(db, logic_id)
        if not existing_logic:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Logic with id {logic_id} not found",
            )

        # Get update data and extract customer_ids for separate handling
        update_data = logic_update.model_dump(exclude_unset=True)
        customer_ids = update_data.pop("customer_ids", None)

        # Extract related entity data for separate handling
        related_entity_data = {}
        related_fields = [
            "demand",
            "market_related_data",
            "solar_and_other_re",
            "interstate_transmission_charges",
            "intrastate_transmission_charges",
            "wheeling_charges",
            "cross_subsidy_surcharges",
            "additional_surcharges",
            "scheduling_and_other_charges",
            "iex_transaction_fees",
            "rec_charges",
            "dsm_charges",
            "tod_time_blocks",
            "tod_base_discom_tariffs",
            "tod_rebates",
            "tod_fuel_surcharges",
            "tod_total_discom_landed_tariffs",
            "inter_state_transmission_losses",
            "intra_state_transmission_losses",
            "wheeling_losses",
            "customer_and_state",
            "settlements",
            "conditions",
            "deployments",
            "forward_testings",
            "backward_testings",
        ]

        for field in related_fields:
            if field in update_data:
                related_entity_data[field] = update_data.pop(field)

        # Handle customers relationship update if customer_ids provided
        if customer_ids is not None:
            validated_customers = logics_db_services.get_customers_by_ids_query(
                db, customer_ids
            )
            if len(validated_customers) != len(customer_ids):
                found_ids = [c.id for c in validated_customers]
                missing_ids = [cid for cid in customer_ids if cid not in found_ids]
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Customers with IDs {missing_ids} not found",
                )
            existing_logic.customers = validated_customers  # Full replacement

        # Update basic logic fields
        existing_logic.updated_by = current_user.user_id

        # Update related entities if provided
        if related_entity_data:
            _update_related_entities(db, logic_id, related_entity_data)

        # Validate draft→published transition
        if (
            "is_draft" in update_data
            and update_data["is_draft"] is False
            and existing_logic.is_draft
        ):
            _validate_logic_completeness_for_publishing(db, logic_id)

        # Update the logic with remaining fields
        updated_logic = logics_db_services.update_logic_query(
            db, existing_logic, update_data
        )

        logger.info(
            f"Logic {logic_id} updated successfully by user {current_user.user_id}"
        )

        return models.LogicUpdateResponse(
            message="Logic updated successfully",
            logic=models.LogicResponse.model_validate(updated_logic),
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating logic {logic_id}: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error updating logic",
        )


def _update_related_entities(
    db: DbSession, logic_id: UUID, related_entity_data: Dict[str, Any]
) -> None:
    """
    Helper function to update related entities for a logic.
    Uses date-based replacement logic for entities with start_date.
    """
    _update_related_entities_with_date_logic(db, logic_id, related_entity_data)


def _validate_logic_completeness_for_publishing(db: DbSession, logic_id: UUID) -> None:
    """
    Validate that a logic has all required relationships before publishing.
    Raises HTTPException if validation fails.
    """
    # Get logic with all relationships
    logic = logics_db_services.get_logic_by_id_with_relationships_query(db, logic_id)
    if not logic:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Logic not found"
        )

    missing_relationships = []

    # Check required relationships - customize based on business rules
    if not logic.demands:
        missing_relationships.append("demand")

    if not logic.market_related_data:
        missing_relationships.append("market_related_data")

    if not logic.customers:
        missing_relationships.append("customers")

    # Add more validation rules as needed based on business requirements
    # Example: if not logic.settlements: missing_relationships.append("settlements")

    if missing_relationships:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Cannot publish logic. Missing required data: {', '.join(missing_relationships)}",
        )


def _update_related_entities_with_date_logic(
    db: DbSession, logic_id: UUID, related_entity_data: Dict[str, Any]
) -> None:
    """
    Helper function to update related entities with date-based replacement logic.
    For entities with start_date, only replace if start_date matches, otherwise create new entries.
    """
    # Handle demand (single entity, no date logic)
    if "demand" in related_entity_data:
        # Clear existing demand
        db.query(Demand).filter(Demand.logic_id == logic_id).delete()

        # Create new demand if data provided
        demand_data = related_entity_data["demand"]
        if demand_data:
            demand_dict = (
                demand_data.model_dump(exclude_unset=True)
                if hasattr(demand_data, "model_dump")
                else demand_data
            )
            if demand_dict:
                demand = Demand(logic_id=logic_id, **demand_dict)
                db.add(demand)

    # Handle entities with date-based logic
    date_based_entities = {
        "interstate_transmission_charges": InterstateTransmissionCharge,
        "intrastate_transmission_charges": IntrastateTransmissionCharge,
        "wheeling_charges": WheelingCharge,
        "cross_subsidy_surcharges": CrossSubsidySurcharge,
        "additional_surcharges": AdditionalSurcharge,
        "scheduling_and_other_charges": SchedulingAndOtherCharge,
        "iex_transaction_fees": IEXTransactionFee,
        "rec_charges": RECCharge,
        "dsm_charges": DSMCharge,
        "inter_state_transmission_losses": InterStateTransmissionLoss,
        "intra_state_transmission_losses": IntraStateTransmissionLoss,
        "wheeling_losses": WheelingLoss,
        "tod_base_discom_tariffs": TodBaseDiscomTariff,
        "tod_rebates": TodRebate,
        "tod_fuel_surcharges": TodFuelSurcharge,
        "tod_total_discom_landed_tariffs": TodTotalDiscomLandedTariff,
    }

    for field_name, entity_class in date_based_entities.items():
        if field_name in related_entity_data:
            entity_data_list = related_entity_data[field_name]
            if entity_data_list:
                for entity_data in entity_data_list:
                    entity_dict = (
                        entity_data.model_dump(exclude_unset=True)
                        if hasattr(entity_data, "model_dump")
                        else entity_data
                    )
                    if entity_dict and "start_date" in entity_dict:
                        start_date = entity_dict["start_date"]

                        # Check if entity with same start_date exists
                        existing_entity = (
                            db.query(entity_class)
                            .filter(
                                getattr(entity_class, "logic_id") == logic_id,
                                getattr(entity_class, "start_date") == start_date,
                            )
                            .first()
                        )

                        if existing_entity:
                            # Update existing entity
                            for field, value in entity_dict.items():
                                if hasattr(existing_entity, field):
                                    setattr(existing_entity, field, value)
                        else:
                            # Create new entity
                            entity = entity_class(logic_id=logic_id, **entity_dict)
                            db.add(entity)

    # Handle non-date-based list entities (full replacement)
    non_date_entities = {
        "market_related_data": MarketRelatedData,
        "solar_and_other_re": SolarAndOtherRe,
        "tod_time_blocks": TodTimeBlock,
        "customer_and_state": CustomerAndState,
        "settlements": Settlement,
        "conditions": Condition,
        "deployments": Deployment,
        "forward_testings": ForwardTesting,
        "backward_testings": BackwardTesting,
    }

    for field_name, entity_class in non_date_entities.items():
        if field_name in related_entity_data:
            # Clear existing entities
            logic_id_field = getattr(entity_class, "logic_id")
            db.query(entity_class).filter(logic_id_field == logic_id).delete()

            # Create new entities if data provided
            entity_data_list = related_entity_data[field_name]
            if entity_data_list:
                for entity_data in entity_data_list:
                    entity_dict = (
                        entity_data.model_dump(exclude_unset=True)
                        if hasattr(entity_data, "model_dump")
                        else entity_data
                    )
                    if entity_dict:
                        entity = entity_class(logic_id=logic_id, **entity_dict)
                        db.add(entity)
