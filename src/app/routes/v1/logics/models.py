from datetime import date, datetime, time
from decimal import Decimal
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field


class LogicResponse(BaseModel):
    """Logic response schema"""

    model_config = ConfigDict(from_attributes=True)

    id: UUID
    logic_id: Optional[str]
    logic_name: Optional[str]
    is_deleted: bool
    is_draft: bool
    created_at: datetime
    created_by: UUID
    updated_at: Optional[datetime]
    updated_by: Optional[UUID]


class DeleteLogicResponse(BaseModel):
    """Response schema for logic deletion"""

    message: str
    logic_id: UUID


# Request models for nested entities
class DemandRequest(BaseModel):
    """Demand data for logic creation/update"""
    noc_quantum: Optional[Decimal] = Field(None, decimal_places=4)
    demand_to_be_bid: Optional[Decimal] = Field(None, decimal_places=4)
    demand_safety_factor: Optional[Decimal] = Field(None, decimal_places=4)


class MarketRelatedDataRequest(BaseModel):
    """Market related data for logic creation/update"""
    market_type_id: Optional[int] = None
    percentage_of_demand: Optional[Decimal] = Field(None, decimal_places=4)
    carry_forward_id: Optional[int] = None
    safety_factor: Optional[str] = Field(None, max_length=100)
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    start_time: Optional[time] = None
    end_time: Optional[time] = None


class SolarAndOtherReRequest(BaseModel):
    """Solar and other renewable energy data for logic creation/update"""
    re_type_id: Optional[int] = None
    capacity: Optional[Decimal] = Field(None, decimal_places=4)
    cuf: Optional[Decimal] = Field(None, decimal_places=4)
    levelized_tariff_power_source: Optional[Decimal] = Field(None, decimal_places=4)
    percentage_of_demand_met_by_re_source: Optional[Decimal] = Field(None, decimal_places=4)
    banking_allowed: Optional[bool] = None
    banking_allowed_description: Optional[str] = None
    banking_charges: Optional[Decimal] = Field(None, decimal_places=4)
    percentage_banking_allowed: Optional[Decimal] = Field(None, decimal_places=4)


class ChargeRequest(BaseModel):
    """Generic charge data for various charge types"""
    charges: Optional[Decimal] = Field(None, decimal_places=4)
    start_date: Optional[date] = None


class LossRequest(BaseModel):
    """Generic loss data for various loss types"""
    losses: Optional[Decimal] = Field(None, decimal_places=4)
    start_date: Optional[date] = None


class TodTimeBlockRequest(BaseModel):
    """TOD time block data for logic creation/update"""
    start_time_block: Optional[time] = None
    start_time_block_start_date: Optional[date] = None
    end_time_block: Optional[time] = None
    end_time_block_start_date: Optional[date] = None


class TodTariffRequest(BaseModel):
    """Generic TOD tariff data"""
    base_discom_tariff: Optional[Decimal] = Field(None, decimal_places=4)
    start_date: Optional[date] = None


class TodRebateRequest(BaseModel):
    """TOD rebate data"""
    rebate: Optional[Decimal] = Field(None, decimal_places=4)
    start_date: Optional[date] = None


class TodFuelSurchargeRequest(BaseModel):
    """TOD fuel surcharge data"""
    fuel_surcharge: Optional[Decimal] = Field(None, decimal_places=4)
    start_date: Optional[date] = None


class TodTotalDiscomLandedTariffRequest(BaseModel):
    """TOD total discom landed tariff data"""
    total_discom_landed_tariff: Optional[Decimal] = Field(None, decimal_places=4)
    start_date: Optional[date] = None


class SettlementRequest(BaseModel):
    """Settlement data for logic creation/update"""
    settlement_type_id: Optional[int] = None


class ConditionRequest(BaseModel):
    """Condition data for logic creation/update"""
    ratio_value_id: Optional[int] = None
    condition_id: Optional[int] = None
    factor_id: Optional[int] = None
    goal_parameter: Optional[str] = Field(None, max_length=255)
    operating_value: Optional[Decimal] = Field(None, decimal_places=4)


class CustomerAndStateRequest(BaseModel):
    """Customer and state data for logic creation/update"""
    customer: Optional[str] = Field(None, max_length=255)
    state: Optional[str] = Field(None, max_length=255)


class DeploymentRequest(BaseModel):
    """Deployment data for logic creation/update"""
    is_deployed: Optional[bool] = None


class ForwardTestingRequest(BaseModel):
    """Forward testing data for logic creation/update"""
    test_status: Optional[str] = Field(None, max_length=100)
    is_active: Optional[bool] = None


class BackwardTestingRequest(BaseModel):
    """Backward testing data for logic creation/update"""
    test_status: Optional[str] = Field(None, max_length=100)
    is_active: Optional[bool] = None


class LogicCreateRequest(BaseModel):
    """Request schema for creating a new logic"""
    logic_name: Optional[str] = Field(None, max_length=255)
    customer_ids: Optional[List[int]] = Field(default=[], description="List of customer IDs to associate with logic")

    # Related entity data
    demand: Optional[DemandRequest] = None
    market_related_data: Optional[List[MarketRelatedDataRequest]] = Field(default=[])
    solar_and_other_re: Optional[List[SolarAndOtherReRequest]] = Field(default=[])
    interstate_transmission_charges: Optional[List[ChargeRequest]] = Field(default=[])
    intrastate_transmission_charges: Optional[List[ChargeRequest]] = Field(default=[])
    wheeling_charges: Optional[List[ChargeRequest]] = Field(default=[])
    cross_subsidy_surcharges: Optional[List[ChargeRequest]] = Field(default=[])
    additional_surcharges: Optional[List[ChargeRequest]] = Field(default=[])
    scheduling_and_other_charges: Optional[List[ChargeRequest]] = Field(default=[])
    iex_transaction_fees: Optional[List[ChargeRequest]] = Field(default=[])
    rec_charges: Optional[List[ChargeRequest]] = Field(default=[])
    dsm_charges: Optional[List[ChargeRequest]] = Field(default=[])

    # TOD related data
    tod_time_blocks: Optional[List[TodTimeBlockRequest]] = Field(default=[])
    tod_base_discom_tariffs: Optional[List[TodTariffRequest]] = Field(default=[])
    tod_rebates: Optional[List[TodRebateRequest]] = Field(default=[])
    tod_fuel_surcharges: Optional[List[TodFuelSurchargeRequest]] = Field(default=[])
    tod_total_discom_landed_tariffs: Optional[List[TodTotalDiscomLandedTariffRequest]] = Field(default=[])

    # Loss data
    inter_state_transmission_losses: Optional[List[LossRequest]] = Field(default=[])
    intra_state_transmission_losses: Optional[List[LossRequest]] = Field(default=[])
    wheeling_losses: Optional[List[LossRequest]] = Field(default=[])

    # Other related data
    customer_and_state: Optional[List[CustomerAndStateRequest]] = Field(default=[])
    settlements: Optional[List[SettlementRequest]] = Field(default=[])
    conditions: Optional[List[ConditionRequest]] = Field(default=[])
    deployments: Optional[List[DeploymentRequest]] = Field(default=[])
    forward_testings: Optional[List[ForwardTestingRequest]] = Field(default=[])
    backward_testings: Optional[List[BackwardTestingRequest]] = Field(default=[])


class LogicUpdateRequest(BaseModel):
    """Request schema for updating an existing logic"""
    logic_name: Optional[str] = Field(None, max_length=255)
    is_draft: Optional[bool] = None
    customer_ids: Optional[List[int]] = Field(None, description="List of customer IDs to associate with logic")

    # Related entity data - all optional for partial updates
    demand: Optional[DemandRequest] = None
    market_related_data: Optional[List[MarketRelatedDataRequest]] = None
    solar_and_other_re: Optional[List[SolarAndOtherReRequest]] = None
    interstate_transmission_charges: Optional[List[ChargeRequest]] = None
    intrastate_transmission_charges: Optional[List[ChargeRequest]] = None
    wheeling_charges: Optional[List[ChargeRequest]] = None
    cross_subsidy_surcharges: Optional[List[ChargeRequest]] = None
    additional_surcharges: Optional[List[ChargeRequest]] = None
    scheduling_and_other_charges: Optional[List[ChargeRequest]] = None
    iex_transaction_fees: Optional[List[ChargeRequest]] = None
    rec_charges: Optional[List[ChargeRequest]] = None
    dsm_charges: Optional[List[ChargeRequest]] = None

    # TOD related data
    tod_time_blocks: Optional[List[TodTimeBlockRequest]] = None
    tod_base_discom_tariffs: Optional[List[TodTariffRequest]] = None
    tod_rebates: Optional[List[TodRebateRequest]] = None
    tod_fuel_surcharges: Optional[List[TodFuelSurchargeRequest]] = None
    tod_total_discom_landed_tariffs: Optional[List[TodTotalDiscomLandedTariffRequest]] = None

    # Loss data
    inter_state_transmission_losses: Optional[List[LossRequest]] = None
    intra_state_transmission_losses: Optional[List[LossRequest]] = None
    wheeling_losses: Optional[List[LossRequest]] = None

    # Other related data
    customer_and_state: Optional[List[CustomerAndStateRequest]] = None
    settlements: Optional[List[SettlementRequest]] = None
    conditions: Optional[List[ConditionRequest]] = None
    deployments: Optional[List[DeploymentRequest]] = None
    forward_testings: Optional[List[ForwardTestingRequest]] = None
    backward_testings: Optional[List[BackwardTestingRequest]] = None


class LogicCreateResponse(BaseModel):
    """Response schema for logic creation"""
    message: str
    logic: LogicResponse


class LogicUpdateResponse(BaseModel):
    """Response schema for logic update"""
    message: str
    logic: LogicResponse
