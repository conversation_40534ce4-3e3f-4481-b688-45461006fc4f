from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict


class LogicResponse(BaseModel):
    """Logic response schema"""

    model_config = ConfigDict(from_attributes=True)

    id: UUID
    logic_id: Optional[str]
    logic_name: Optional[str]
    is_deleted: bool
    is_draft: bool
    created_at: datetime
    created_by: UUID
    updated_at: Optional[datetime]
    updated_by: Optional[UUID]


class DeleteLogicResponse(BaseModel):
    """Response schema for logic deletion"""

    message: str
    logic_id: UUID
