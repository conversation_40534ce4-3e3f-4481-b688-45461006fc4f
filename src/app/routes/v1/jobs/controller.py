from database.core import DbSession
from fastapi import APIRouter, HTTPException, status
from routes.v1.auth.service import CurrentUser
from tasks import sample
from utils.logger import get_controller_logger

logger = get_controller_logger("jobs")
router = APIRouter(prefix="/v1/jobs", tags=["Jobs"])


@router.post("/start_sample_job", status_code=status.HTTP_202_ACCEPTED)
async def start_sample_job(
    db: DbSession,
    current_user: CurrentUser,
):
    logger.info("Starting a sample job.")
    try:
        task = sample.sample_job.delay()
        logger.info(f"Sample job started with task ID: {task.id}")
        job = {"task_id": task.id, "status": "started"}
        return job
    except Exception as e:
        logger.error(f"Failed to start sample job: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to start sample job")
