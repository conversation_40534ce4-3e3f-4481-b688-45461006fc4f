from typing import Annotated

from database.core import DbSession
from fastapi import APIRouter, Depends, Request, status
from fastapi.security import OAuth2PasswordRequestForm
from utils.logger import get_controller_logger
from utils.rate_limiter import limiter

from . import models, service

logger = get_controller_logger("auth")
router = APIRouter(prefix="/v1/auth", tags=["auth"])


@router.post("/register", response_model=models.UserResponse)
@limiter.limit("5/hour")
async def create_user(
    request: Request, create_user_request: models.CreateUserRequest, db: DbSession
):
    logger.info(f"User registration attempt: {create_user_request.email}")
    try:
        user = service.create_user(create_user_request, db)
        logger.info(f"Successfully registered user: {user.email}")
        return user
    except Exception as e:
        logger.error(f"Registration failed for {create_user_request.email}: {str(e)}")
        raise


@router.post("/login", response_model=models.Token)
async def login_for_access_token(
    form_data: Annotated[OAuth2PasswordRequestForm, Depends()], db: DbSession
):
    logger.info(f"Login attempt: {form_data.username}")
    try:
        result = service.login_for_access_token(form_data, db)
        logger.info(f"Successful login: {form_data.username}")
        return result
    except Exception:
        logger.warning(f"Failed login: {form_data.username}")
        raise


@router.post("/refresh-token", response_model=models.Token)
async def refresh_access_token(
    refresh_token: models.RefreshTokenRequest, db: DbSession
):
    try:
        return service.refresh_access_token(refresh_token, db)
    except Exception as e:
        logger.warning(f"Token refresh failed: {str(e)}")
        raise


@router.post("/forgot-password")
async def forgot_password(
    fpr: models.ForgotPasswordRequest,
    request: Request,
    db: DbSession,
):
    logger.info(f"Password reset request: {fpr.email}")
    try:
        result = service.forgot_password(fpr, request, db)
        return result
    except Exception as e:
        logger.error(f"Password reset failed for {fpr.email}: {str(e)}")
        raise


@router.post("/reset-password", status_code=status.HTTP_200_OK)
async def reset_password(rfp: models.ResetPassword, db: DbSession):
    logger.info("Password reset attempt")
    try:
        return service.reset_password(rfp, db)
    except Exception as e:
        logger.error(f"Password reset failed: {str(e)}")
        raise
