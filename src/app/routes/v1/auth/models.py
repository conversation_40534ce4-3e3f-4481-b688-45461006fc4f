from uuid import UUID

from pydantic import BaseModel, ConfigDict, EmailStr, Field, field_validator
from utils.password_validation import validate_password_strength


class UserResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    id: UUID
    email: EmailStr
    first_name: str
    last_name: str
    is_active: bool


class CreateUserRequest(BaseModel):
    first_name: str = Field(..., min_length=1, max_length=100)
    last_name: str = Field(..., min_length=1, max_length=100)
    email: EmailStr
    password: str = Field(..., min_length=8)

    @field_validator("password")
    def validate_password(cls, v):
        """Validate password strength using comprehensive rules"""
        return validate_password_strength(v)

    @field_validator("first_name", "last_name")
    def validate_names(cls, v):
        """Validate name fields"""
        v = v.strip()
        if not v:
            raise ValueError("Name cannot be empty or only whitespace")
        if any(char.isdigit() for char in v):
            raise ValueError("Names cannot contain numbers")
        return v.title()  # Capitalize properly


class Token(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str


class TokenData(BaseModel):
    user_id: str | None = None

    def get_uuid(self) -> UUID | None:
        if self.user_id:
            return UUID(self.user_id)
        return None


class RefreshTokenRequest(BaseModel):
    refresh_token: str


class ForgotPasswordRequest(BaseModel):
    email: EmailStr


class ResetPassword(BaseModel):
    reset_token: str
    new_password: str = Field(..., min_length=8)
    confirm_password: str = Field(..., min_length=8)

    @field_validator("new_password")
    def validate_new_password(cls, v):
        """Validate password strength using comprehensive rules"""
        return validate_password_strength(v)

    @field_validator("confirm_password")
    def passwords_match(cls, v, info):
        """Validate that passwords match"""
        if "new_password" in info.data and v != info.data["new_password"]:
            raise ValueError("Passwords do not match")
        return v
