"""
V1 route registration.
"""

from fastapi import FastAPI

from routes.v1.auth.controller import router as auth_router
from routes.v1.customers.controller import router as customers_router
from routes.v1.jobs.controller import router as jobs_router
from routes.v1.logics.controller import router as logics_router
from routes.v1.lookups.controller import router as lookups_router


def register_v1_routes(app: FastAPI):
    """Register all V1 API routes."""
    app.include_router(auth_router, deprecated=False)
    app.include_router(customers_router, deprecated=False)
    app.include_router(jobs_router, deprecated=False)
    app.include_router(lookups_router, deprecated=False)
    app.include_router(logics_router, deprecated=False)
