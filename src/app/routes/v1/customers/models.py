from datetime import date
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field, field_validator


class StateResponse(BaseModel):
    """State response for many-to-many relationship"""

    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str


class LogicResponse(BaseModel):
    """Logic response for many-to-many relationship"""

    model_config = ConfigDict(from_attributes=True)

    id: UUID
    logic_id: Optional[str]
    logic_name: Optional[str]


class CustomerGetResponse(BaseModel):
    """Minimal customer response schema"""

    model_config = ConfigDict(from_attributes=True)
    id: int
    customer_id: str
    customer_name: Optional[str]
    onboarded_date: Optional[date]
    states: List[StateResponse] = Field(default=[])
    logics: List[LogicResponse] = Field(default=[])


class CustomerCreate(BaseModel):
    customer_name: str
    onboarded_date: Optional[date]
    state_ids: List[int] = []


class CustomerUpdateRequest(BaseModel):
    """Schema for partial customer updates"""

    customer_name: Optional[str] = Field(
        None, max_length=255, description="Customer name"
    )
    onboarded_date: Optional[date] = Field(None, description="Customer onboarding date")
    is_deleted: Optional[bool] = Field(None, description="Customer deleted status")
    state_ids: Optional[List[int]] = Field(
        None, description="List of state IDs to associate with customer"
    )

    @field_validator("state_ids")
    @classmethod
    def validate_state_ids(cls, states: Optional[List[str]]) -> Optional[List[str]]:
        if states is not None and len(states) != len(set(states)):
            raise ValueError("Duplicate state IDs not allowed")
        return states

    class Config:
        json_schema_extra = {
            "example": {
                "customer_name": "Updated Customer Name",
                "is_deleted": False,
                "state_ids": [1, 2, 3],
            }
        }


class PaginatedCustomerResponse(BaseModel):
    """Paginated response with metadata"""

    customers: List[CustomerGetResponse]
    total: int
    skip: int
    limit: int
    has_next: bool

    model_config = ConfigDict(from_attributes=True)
