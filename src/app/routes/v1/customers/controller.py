from typing import Optional

from database.core import DbSession
from fastapi import APIRouter, HTTPException, Query, status
from utils.logger import get_controller_logger

from ..auth.service import CurrentUser
from . import models, service

logger = get_controller_logger("customers_controller")
router = APIRouter(prefix="/v1/customers", tags=["Customers"])


@router.get("/", response_model=models.PaginatedCustomerResponse)
def list_customers(
    db: DbSession,
    current_user: CurrentUser,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = None,
):
    """Get all customers"""
    try:
        if search:
            search = search.strip()
        result = service.get_all_customers(db, skip=skip, limit=limit, search=search)
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving customers: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving customers",
        )


@router.post("/", status_code=201)
def add_customer(
    db: DbSession,
    current_user: CurrentUser,
    customer_create: models.CustomerCreate,
):
    """Create a new customer"""
    try:
        return service.create_customer(db, current_user, customer_create)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating customer: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error creating customer",
        )


@router.get("/{id}", response_model=models.CustomerGetResponse)
def find_customer_by_id(
    db: DbSession,
    current_user: CurrentUser,
    id: int,
):
    """Get customer by ID"""
    try:
        customer = service.get_customer_by_id(db, id)
        if not customer:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Customer with ID {id} not found",
            )
        return customer
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving customer: {str(e)}",
        )


@router.patch("/{id}", response_model=models.CustomerGetResponse)
def modify_customer(
    db: DbSession,
    current_user: CurrentUser,
    id: int,
    customer_update: models.CustomerUpdateRequest,
):
    """Update customer by ID"""
    try:
        customer = service.get_customer_by_id(db, id)
        if not customer:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Customer with ID {id} not found",
            )
        return service.update_customer(db, current_user, id, customer_update)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating customer: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error updating customer",
        )


@router.delete("/{id}", status_code=status.HTTP_204_NO_CONTENT)
def remove_customer(
    db: DbSession,
    current_user: CurrentUser,
    id: int,
):
    """Delete customer by ID"""
    try:
        customer = service.get_customer_by_id(db, id)
        if not customer:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Customer with ID {id} not found",
            )
        service.delete_customer(db, customer)
        return {"detail": "Customer deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting customer: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error deleting customer",
        )
