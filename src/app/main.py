import logging
import os
from contextlib import asynccontextmanager

from database.core import Base, engine
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from routes import register_routes

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager for startup and shutdown events."""
    # Startup
    # Skip database initialization during tests
    if not os.getenv("SKIP_DB_INIT", "false").lower() == "true":
        Base.metadata.create_all(bind=engine)

    yield

    # Shutdown (cleanup if needed)


app = FastAPI(
    title="Kimbal Logic Builder",
    version="1.0.0",
    description="Kimbal Logic Builder API",
    lifespan=lifespan,
)

allowed_origins = os.getenv("ALLOWED_ORIGINS", "http://localhost:3000").split(",")

app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Register routes
register_routes(app)


@app.get("/")
async def root():
    return {"message": "Welcome to Kimbal Logic Builder API"}


@app.get("/health")
async def health():
    return {"status": "ok"}
