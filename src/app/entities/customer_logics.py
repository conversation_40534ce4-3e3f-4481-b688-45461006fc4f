from database.core import Base
from sqlalchemy import <PERSON>umn, <PERSON><PERSON><PERSON>, Integer, Table
from sqlalchemy.dialects.postgresql import UUID

customer_logics = Table(
    "customer_logics",
    Base.metadata,
    Column("customer_id", Integer, ForeignKey("customers.id"), primary_key=True),
    Column("logic_id", (UUID(as_uuid=True)), ForeignKey("logics.id"), primary_key=True),
    extend_existing=True,  # should be removed in prod
)
