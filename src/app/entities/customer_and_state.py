from database.core import Base
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship


class CustomerAndState(Base):
    __tablename__ = "customer_and_state"

    id = Column(Integer, primary_key=True)
    logic_id = Column((UUID(as_uuid=True)), ForeignKey("logics.id"))
    customer = Column(String(255))
    state = Column(String(255))

    # Relationships
    logic = relationship("Logic", back_populates="customer_and_state")
