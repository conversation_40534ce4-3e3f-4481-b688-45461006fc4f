from database.core import Base
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship


class BackwardTesting(Base):
    __tablename__ = "backward_testing"

    id = Column(Integer, primary_key=True, index=True)
    logic_id = Column(UUID(as_uuid=True), ForeignKey("logics.id"))
    test_status = Column(String(100))
    is_active = Column(Boolean, default=True)

    # Relationships
    logic = relationship("Logic", back_populates="backward_testings")
