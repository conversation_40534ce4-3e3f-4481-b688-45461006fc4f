from database.core import Base
from sqlalchemy import (
    Column,
    Date,
    DateTime,
    ForeignKey,
    Integer,
    Numeric,
    String,
    Time,
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func


class MarketRelatedData(Base):
    __tablename__ = "market_related_data"

    id = Column(Integer, primary_key=True)
    logic_id = Column(UUID(as_uuid=True), ForeignKey("logics.id"))
    market_type_id = Column(Integer, ForeignKey("market_types.id"))
    percentage_of_demand = Column(Numeric(15, 4))
    carry_forward_id = Column(Integer, ForeignKey("carry_forwards.id"))
    safety_factor = Column(String(100))
    start_date = Column(Date)
    end_date = Column(Date)
    start_time = Column(Time)
    end_time = Column(Time)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    market_type = relationship("MarketType")
    carry_forward = relationship("Carry_forwards")
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    logic = relationship("Logic", back_populates="market_related_data")
