from database.core import Base
from sqlalchemy import (
    Column,
    Date,
    DateTime,
    Foreign<PERSON>ey,
    Integer,
    Numeric,
    Time,
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func


class TodTimeBlock(Base):
    __tablename__ = "tod_time_blocks"

    id = Column(Integer, primary_key=True, index=True)
    logic_id = Column(UUID(as_uuid=True), ForeignKey("logics.id"), nullable=False)
    start_time_block = Column(Time)
    start_time_block_start_date = Column(Date)
    end_time_block = Column(Time)
    end_time_block_start_date = Column(Date)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    logic = relationship("Logic", back_populates="tod_time_blocks")


class TodBaseDiscomTariff(Base):
    __tablename__ = "tod_base_discom_tariffs"

    id = Column(Integer, primary_key=True, index=True)
    logic_id = Column(UUID(as_uuid=True), ForeignKey("logics.id"), nullable=False)
    base_discom_tariff = Column(Numeric(15, 4))
    start_date = Column(Date)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    logic = relationship("Logic", back_populates="tod_base_discom_tariffs")


class TodRebate(Base):
    __tablename__ = "tod_rebates"

    id = Column(Integer, primary_key=True, index=True)
    logic_id = Column(UUID(as_uuid=True), ForeignKey("logics.id"), nullable=False)
    rebate = Column(Numeric(15, 4))
    start_date = Column(Date)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    logic = relationship("Logic", back_populates="tod_rebates")


class TodFuelSurcharge(Base):
    __tablename__ = "tod_fuel_surcharges"

    id = Column(Integer, primary_key=True, index=True)
    logic_id = Column(UUID(as_uuid=True), ForeignKey("logics.id"), nullable=False)
    fuel_surcharge = Column(Numeric(15, 4))
    start_date = Column(Date)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    logic = relationship("Logic", back_populates="tod_fuel_surcharges")


class TodTotalDiscomLandedTariff(Base):
    __tablename__ = "tod_total_discom_landed_tariffs"

    id = Column(Integer, primary_key=True, index=True)
    logic_id = Column(UUID(as_uuid=True), ForeignKey("logics.id"), nullable=False)
    total_discom_landed_tariff = Column(Numeric(15, 4))
    start_date = Column(Date)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    logic = relationship("Logic", back_populates="tod_total_discom_landed_tariffs")
