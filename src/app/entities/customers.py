from database.core import Base
from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    <PERSON>umn,
    Comp<PERSON>,
    Date,
    DateTime,
    Foreign<PERSON><PERSON>,
    Integer,
    String,
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from .customer_logics import customer_logics
from .customer_states import customer_states


class Customer(Base):
    __tablename__ = "customers"
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, primary_key=True, autoincrement=True)
    customer_id = Column(
        String(100),
        Computed("'EUWI-' || LPAD(id::text, 4, '0')"),
        unique=True,
        nullable=False,
    )
    customer_name = Column(String(255), nullable=True)
    onboarded_date = Column(Date, nullable=True)
    is_deleted = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id"))

    states = relationship(
        "State", secondary=customer_states, back_populates="customers"
    )
    logics = relationship(
        "Logic", secondary=customer_logics, back_populates="customers"
    )
    creator = relationship(
        "User", foreign_keys=[created_by], back_populates="created_customers"
    )

    updater = relationship(
        "User", foreign_keys=[updated_by], back_populates="updated_customers"
    )
