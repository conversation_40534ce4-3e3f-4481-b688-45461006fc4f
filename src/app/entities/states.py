from database.core import Base
from sqlalchemy import Column, Integer, String
from sqlalchemy.orm import relationship

from .customer_states import customer_states


class State(Base):
    __tablename__ = "states"
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True)

    # Many-to-many back reference
    customers = relationship(
        "Customer", secondary=customer_states, back_populates="states"
    )
