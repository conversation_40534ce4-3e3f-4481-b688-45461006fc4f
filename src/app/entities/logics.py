import uuid

from database.core import Base
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>mp<PERSON>, DateTime, Foreign<PERSON>ey, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from .customer_logics import customer_logics


class Logic(Base):
    __tablename__ = "logics"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    logic_id = Column(
        String(100),
        Computed("'LOGIC-' || UPPER(SUBSTRING(id::text, 1, 8))"),
        unique=True,
        nullable=False,
    )
    is_deleted = Column(Boolean, default=False)
    is_draft = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    updated_by = Column(UUID(as_uuid=True), Foreign<PERSON>ey("users.id"))

    # Relationships

    creator = relationship(
        "User", foreign_keys=[created_by], back_populates="created_logics"
    )
    updater = relationship(
        "User", foreign_keys=[updated_by], back_populates="updated_logics"
    )

    demands = relationship("Demand", back_populates="logic")
    market_related_data = relationship("MarketRelatedData", back_populates="logic")
    solar_and_other_re = relationship("SolarAndOtherRe", back_populates="logic")
    interstate_transmission_charges = relationship(
        "InterstateTransmissionCharge", back_populates="logic"
    )
    intrastate_transmission_charges = relationship(
        "IntrastateTransmissionCharge", back_populates="logic"
    )
    wheeling_charges = relationship("WheelingCharge", back_populates="logic")
    cross_subsidy_surcharges = relationship(
        "CrossSubsidySurcharge", back_populates="logic"
    )
    additional_surcharges = relationship("AdditionalSurcharge", back_populates="logic")
    scheduling_and_other_charges = relationship(
        "SchedulingAndOtherCharge", back_populates="logic"
    )
    iex_transaction_fees = relationship("IEXTransactionFee", back_populates="logic")
    rec_charges = relationship("RECCharge", back_populates="logic")
    dsm_charges = relationship("DSMCharge", back_populates="logic")
    tod_time_blocks = relationship(
        "TodTimeBlock", back_populates="logic", cascade="all, delete-orphan"
    )
    tod_base_discom_tariffs = relationship(
        "TodBaseDiscomTariff", back_populates="logic", cascade="all, delete-orphan"
    )
    tod_rebates = relationship(
        "TodRebate", back_populates="logic", cascade="all, delete-orphan"
    )
    tod_fuel_surcharges = relationship(
        "TodFuelSurcharge", back_populates="logic", cascade="all, delete-orphan"
    )
    tod_total_discom_landed_tariffs = relationship(
        "TodTotalDiscomLandedTariff",
        back_populates="logic",
        cascade="all, delete-orphan",
    )
    inter_state_transmission_losses = relationship(
        "InterStateTransmissionLoss", back_populates="logic"
    )
    intra_state_transmission_losses = relationship(
        "IntraStateTransmissionLoss", back_populates="logic"
    )
    wheeling_losses = relationship("WheelingLoss", back_populates="logic")
    customer_and_state = relationship("CustomerAndState", back_populates="logic")
    settlements = relationship("Settlement", back_populates="logic")
    conditions = relationship("Condition", back_populates="logic")
    deployments = relationship("Deployment", back_populates="logic")
    forward_testings = relationship("ForwardTesting", back_populates="logic")
    backward_testings = relationship("BackwardTesting", back_populates="logic")

    # Many-to-many with Customer
    customers = relationship(
        "Customer", secondary=customer_logics, back_populates="logics"
    )
