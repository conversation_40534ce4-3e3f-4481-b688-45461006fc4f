from database.core import Base
from sqlalchemy import Column, Date, DateTime, Foreign<PERSON>ey, Integer, Numeric
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func


class InterStateTransmissionLoss(Base):
    __tablename__ = "inter_state_transmission_losses"
    id = Column(Integer, primary_key=True)
    logic_id = Column(UUID(as_uuid=True), ForeignKey("logics.id"))
    losses = Column(Numeric(15, 4))
    start_date = Column(Date)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    # Relationships
    logic = relationship("Logic", back_populates="inter_state_transmission_losses")


class IntraStateTransmissionLoss(Base):
    __tablename__ = "intra_state_transmission_losses"
    id = Column(Integer, primary_key=True)
    logic_id = Column(UUID(as_uuid=True), Foreign<PERSON>ey("logics.id"))
    losses = Column(Numeric(15, 4))
    start_date = Column(Date)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    # Relationships
    logic = relationship("Logic", back_populates="intra_state_transmission_losses")


class WheelingLoss(Base):
    __tablename__ = "wheeling_losses"
    id = Column(Integer, primary_key=True)
    logic_id = Column(UUID(as_uuid=True), ForeignKey("logics.id"))
    losses = Column(Numeric(15, 4))
    start_date = Column(Date)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    # Relationships
    logic = relationship("Logic", back_populates="wheeling_losses")
