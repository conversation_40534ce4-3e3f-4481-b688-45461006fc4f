from database.core import Base
from sqlalchemy import Column, <PERSON><PERSON><PERSON>, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship


class Settlement(Base):
    __tablename__ = "settlement"

    id = Column(Integer, primary_key=True, index=True)
    logic_id = Column(UUID(as_uuid=True), ForeignKey("logics.id"))
    settlement_type_id = Column(Integer, ForeignKey("settlement_types.id"))

    # Relationships
    logic = relationship("Logic", back_populates="settlements")
    settlement_type = relationship("SettlementType")
