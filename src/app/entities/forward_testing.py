from database.core import Base
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship


class ForwardTesting(Base):
    __tablename__ = "forward_testing"

    id = Column(Integer, primary_key=True)
    logic_id = Column(UUID(as_uuid=True), ForeignKey("logics.id"))
    test_status = Column(String(100))
    is_active = Column(Boolean, default=False)

    # Relationships
    logic = relationship("Logic", back_populates="forward_testings")
