from database.core import Base
from sqlalchemy import Column, Date, DateTime, Foreign<PERSON>ey, Integer, Numeric
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func


class InterstateTransmissionCharge(Base):
    __tablename__ = "interstate_transmission_charges"
    id = Column(Integer, primary_key=True, index=True)
    logic_id = Column(UUID(as_uuid=True), ForeignKey("logics.id"))
    charges = Column(Numeric(15, 4))
    start_date = Column(Date)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    logic = relationship("Logic", back_populates="interstate_transmission_charges")


class IntrastateTransmissionCharge(Base):
    __tablename__ = "intrastate_transmission_charges"
    id = Column(Integer, primary_key=True, index=True)
    logic_id = Column(UUID(as_uuid=True), Foreign<PERSON>ey("logics.id"))
    charges = Column(Numeric(15, 4))
    start_date = Column(Date)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    logic = relationship("Logic", back_populates="intrastate_transmission_charges")


class WheelingCharge(Base):
    __tablename__ = "wheeling_charges"
    id = Column(Integer, primary_key=True, index=True)
    logic_id = Column(UUID(as_uuid=True), ForeignKey("logics.id"))
    charges = Column(Numeric(15, 4))
    start_date = Column(Date)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    logic = relationship("Logic", back_populates="wheeling_charges")


class CrossSubsidySurcharge(Base):
    __tablename__ = "cross_subsidy_surcharges"
    id = Column(Integer, primary_key=True, index=True)
    logic_id = Column(UUID(as_uuid=True), ForeignKey("logics.id"))
    charges = Column(Numeric(15, 4))
    start_date = Column(Date)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    logic = relationship("Logic", back_populates="cross_subsidy_surcharges")


class AdditionalSurcharge(Base):
    __tablename__ = "additional_surcharges"
    id = Column(Integer, primary_key=True, index=True)
    logic_id = Column(UUID(as_uuid=True), ForeignKey("logics.id"))
    charges = Column(Numeric(15, 4))
    start_date = Column(Date)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    logic = relationship("Logic", back_populates="additional_surcharges")


class SchedulingAndOtherCharge(Base):
    __tablename__ = "scheduling_and_other_charges"
    id = Column(Integer, primary_key=True, index=True)
    logic_id = Column(UUID(as_uuid=True), ForeignKey("logics.id"))
    charges = Column(Numeric(15, 4))
    start_date = Column(Date)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    logic = relationship("Logic", back_populates="scheduling_and_other_charges")


class IEXTransactionFee(Base):
    __tablename__ = "iex_transaction_fees"
    id = Column(Integer, primary_key=True, index=True)
    logic_id = Column(UUID(as_uuid=True), ForeignKey("logics.id"))
    charges = Column(Numeric(15, 4))
    start_date = Column(Date)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    logic = relationship("Logic", back_populates="iex_transaction_fees")


class RECCharge(Base):
    __tablename__ = "rec_charges"
    id = Column(Integer, primary_key=True, index=True)
    logic_id = Column(UUID(as_uuid=True), ForeignKey("logics.id"))
    charges = Column(Numeric(15, 4))
    start_date = Column(Date)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    logic = relationship("Logic", back_populates="rec_charges")


class DSMCharge(Base):
    __tablename__ = "dsm_charges"
    id = Column(Integer, primary_key=True, index=True)
    logic_id = Column(UUID(as_uuid=True), ForeignKey("logics.id"))
    charges = Column(Numeric(15, 4))
    start_date = Column(Date)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    logic = relationship("Logic", back_populates="dsm_charges")
