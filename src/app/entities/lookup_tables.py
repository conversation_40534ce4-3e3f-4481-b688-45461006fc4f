from database.core import Base
from sqlalchemy import Column, Integer, String


class MarketType(Base):
    __tablename__ = "market_types"
    id = Column(Integer, primary_key=True)
    market_type = Column(String(50), unique=True, nullable=False)


class RenewableEnergyType(Base):
    __tablename__ = "renewable_energy_types"
    id = Column(Integer, primary_key=True)
    renewable_energy_type = Column(String(50), unique=True, nullable=False)


class SettlementType(Base):
    __tablename__ = "settlement_types"
    id = Column(Integer, primary_key=True)
    settlement_type = Column(String(50), unique=True, nullable=False)


class ConditionType(Base):
    __tablename__ = "condition_types"
    id = Column(Integer, primary_key=True)
    condition = Column(String(50), unique=True, nullable=False)


class Ratio_value(Base):
    __tablename__ = "ratio_values"

    id = Column(Integer, primary_key=True)
    ratio_value = Column(String(255), unique=True, nullable=False)


class Factor(Base):
    __tablename__ = "factors"

    id = Column(Integer, primary_key=True)
    factor = Column(String(100), unique=True, nullable=False)


class Carry_forwards(Base):
    __tablename__ = "carry_forwards"

    id = Column(Integer, primary_key=True)
    carry_forward = Column(String(100), unique=True, nullable=False)
