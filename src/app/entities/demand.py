from database.core import Base
from sqlalchemy import Column, DateTime, ForeignKey, Integer, Numeric
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func


class Demand(Base):
    __tablename__ = "demand"

    id = Column(Integer, primary_key=True)
    logic_id = Column(UUID(as_uuid=True), ForeignKey("logics.id"))
    noc_quantum = Column(Numeric(15, 4))
    demand_to_be_bid = Column(Numeric(15, 4))
    demand_safety_factor = Column(Numeric(15, 4))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    logic = relationship("Logic", back_populates="demands")
