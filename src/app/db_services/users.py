from database.core import DbSession
from entities.users import User


def create_user(db: DbSession, user: User):
    db.add(user)
    db.commit()
    db.refresh(user)
    return user


def update_user(db: DbSession, user: User):
    db.commit()
    db.refresh(user)
    return user


def get_user_by_id(db: DbSession, user_id: int):
    return db.query(User).filter(User.id == user_id).first()


def get_user_by_email(db: DbSession, email: str):
    return db.query(User).filter(User.email == email).first()
