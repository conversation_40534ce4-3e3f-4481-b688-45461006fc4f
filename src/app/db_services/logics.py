from typing import Dict, List, Optional
from uuid import UUID

from entities.backward_testing import BackwardTesting
from entities.conditions import Condition
from entities.customer_and_state import CustomerAndState
from entities.customers import Customer
from entities.demand import Demand
from entities.deployments import Deployment
from entities.forward_testing import ForwardTesting
from entities.list_of_losses import (
    InterStateTransmissionLoss,
    IntraStateTransmissionLoss,
    WheelingLoss,
)
from entities.logics import Logic
from entities.market_related_data import MarketRelatedData
from entities.open_access_charge_general_information import (
    AdditionalSurcharge,
    CrossSubsidySurcharge,
    DSMCharge,
    IEXTransactionFee,
    InterstateTransmissionCharge,
    IntrastateTransmissionCharge,
    RECCharge,
    SchedulingAndOtherCharge,
    WheelingCharge,
)
from entities.open_access_charges_tod import (
    TodBaseDiscomTariff,
    TodFuelSurcharge,
    TodRebate,
    TodTimeBlock,
    TodTotalDiscomLandedTariff,
)
from entities.settlement import Settlement
from entities.solar_and_other_re import SolarAndOtherRe
from sqlalchemy.orm import Session, joinedload


def get_logic_by_id_query(db: Session, logic_id: UUID) -> Optional[Logic]:
    """
    Query to fetch a logic by ID.
    """
    return (
        db.query(Logic)
        .filter(Logic.id == logic_id, Logic.is_deleted.is_(False))
        .first()
    )


def get_logic_by_id_with_relationships_query(db: Session, logic_id: UUID) -> Optional[Logic]:
    """
    Query to fetch a logic by ID with all relationships loaded.
    """
    return (
        db.query(Logic)
        .options(
            joinedload(Logic.customers),
            joinedload(Logic.demands),
            joinedload(Logic.market_related_data),
            joinedload(Logic.solar_and_other_re),
            joinedload(Logic.interstate_transmission_charges),
            joinedload(Logic.intrastate_transmission_charges),
            joinedload(Logic.wheeling_charges),
            joinedload(Logic.cross_subsidy_surcharges),
            joinedload(Logic.additional_surcharges),
            joinedload(Logic.scheduling_and_other_charges),
            joinedload(Logic.iex_transaction_fees),
            joinedload(Logic.rec_charges),
            joinedload(Logic.dsm_charges),
            joinedload(Logic.tod_time_blocks),
            joinedload(Logic.tod_base_discom_tariffs),
            joinedload(Logic.tod_rebates),
            joinedload(Logic.tod_fuel_surcharges),
            joinedload(Logic.tod_total_discom_landed_tariffs),
            joinedload(Logic.inter_state_transmission_losses),
            joinedload(Logic.intra_state_transmission_losses),
            joinedload(Logic.wheeling_losses),
            joinedload(Logic.customer_and_state),
            joinedload(Logic.settlements),
            joinedload(Logic.conditions),
            joinedload(Logic.deployments),
            joinedload(Logic.forward_testings),
            joinedload(Logic.backward_testings),
        )
        .filter(Logic.id == logic_id, Logic.is_deleted.is_(False))
        .first()
    )


def create_logic_query(db: Session, logic: Logic) -> Logic:
    """
    Create logic in database with auto-generated logic_id.
    """
    db.add(logic)
    db.commit()
    db.refresh(logic)
    return logic


def update_logic_query(
    db: Session, logic: Logic, update_data: Optional[Dict] = None
) -> Logic:
    """
    Query to update an existing logic.
    """
    if update_data:
        for field, value in update_data.items():
            if hasattr(logic, field):
                setattr(logic, field, value)

    db.commit()
    db.refresh(logic)
    return logic


def delete_logic_query(db: Session, logic: Logic) -> None:
    """
    Query to delete a logic (soft delete).
    """
    setattr(logic, "is_deleted", True)
    db.commit()


def get_customers_by_ids_query(db: Session, customer_ids: List[int]) -> List[Customer]:
    """
    Query to fetch customers by their IDs.
    """
    return (
        db.query(Customer)
        .filter(Customer.id.in_(customer_ids), Customer.is_deleted.is_(False))
        .all()
    )


def clear_logic_related_entities_query(db: Session, logic_id: UUID) -> None:
    """
    Clear all related entities for a logic (for full replacement during update).
    This is used when updating with new data to replace existing relationships.
    """
    # Clear entities with cascade="all, delete-orphan" - these will be auto-deleted
    # when the relationship is cleared

    # Clear non-cascading entities manually
    db.query(Demand).filter(Demand.logic_id == logic_id).delete()
    db.query(MarketRelatedData).filter(MarketRelatedData.logic_id == logic_id).delete()
    db.query(SolarAndOtherRe).filter(SolarAndOtherRe.logic_id == logic_id).delete()
    db.query(InterstateTransmissionCharge).filter(InterstateTransmissionCharge.logic_id == logic_id).delete()
    db.query(IntrastateTransmissionCharge).filter(IntrastateTransmissionCharge.logic_id == logic_id).delete()
    db.query(WheelingCharge).filter(WheelingCharge.logic_id == logic_id).delete()
    db.query(CrossSubsidySurcharge).filter(CrossSubsidySurcharge.logic_id == logic_id).delete()
    db.query(AdditionalSurcharge).filter(AdditionalSurcharge.logic_id == logic_id).delete()
    db.query(SchedulingAndOtherCharge).filter(SchedulingAndOtherCharge.logic_id == logic_id).delete()
    db.query(IEXTransactionFee).filter(IEXTransactionFee.logic_id == logic_id).delete()
    db.query(RECCharge).filter(RECCharge.logic_id == logic_id).delete()
    db.query(DSMCharge).filter(DSMCharge.logic_id == logic_id).delete()
    db.query(InterStateTransmissionLoss).filter(InterStateTransmissionLoss.logic_id == logic_id).delete()
    db.query(IntraStateTransmissionLoss).filter(IntraStateTransmissionLoss.logic_id == logic_id).delete()
    db.query(WheelingLoss).filter(WheelingLoss.logic_id == logic_id).delete()
    db.query(CustomerAndState).filter(CustomerAndState.logic_id == logic_id).delete()
    db.query(Settlement).filter(Settlement.logic_id == logic_id).delete()
    db.query(Condition).filter(Condition.logic_id == logic_id).delete()
    db.query(Deployment).filter(Deployment.logic_id == logic_id).delete()
    db.query(ForwardTesting).filter(ForwardTesting.logic_id == logic_id).delete()
    db.query(BackwardTesting).filter(BackwardTesting.logic_id == logic_id).delete()

    db.commit()


def create_charge_entities(db: Session, logic_id: UUID, charge_data: List[Dict], entity_class) -> List:
    """
    Helper function to create charge-type entities (interstate, intrastate, wheeling, etc.)
    """
    entities = []
    for data in charge_data:
        entity = entity_class(logic_id=logic_id, **data)
        entities.append(entity)
        db.add(entity)
    return entities


def create_loss_entities(db: Session, logic_id: UUID, loss_data: List[Dict], entity_class) -> List:
    """
    Helper function to create loss-type entities
    """
    entities = []
    for data in loss_data:
        entity = entity_class(logic_id=logic_id, **data)
        entities.append(entity)
        db.add(entity)
    return entities


def create_tod_entities(db: Session, logic_id: UUID, tod_data: List[Dict], entity_class) -> List:
    """
    Helper function to create TOD-type entities
    """
    entities = []
    for data in tod_data:
        entity = entity_class(logic_id=logic_id, **data)
        entities.append(entity)
        db.add(entity)
    return entities


def create_generic_entities(db: Session, logic_id: UUID, entity_data: List[Dict], entity_class) -> List:
    """
    Helper function to create generic entities with logic_id
    """
    entities = []
    for data in entity_data:
        entity = entity_class(logic_id=logic_id, **data)
        entities.append(entity)
        db.add(entity)
    return entities
