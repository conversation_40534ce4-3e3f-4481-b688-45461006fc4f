from typing import Optional
from uuid import UUID

from entities.logics import Logic
from sqlalchemy.orm import Session


def get_logic_by_id_query(db: Session, logic_id: UUID) -> Optional[Logic]:
    """
    Query to fetch a logic by ID.
    """
    return (
        db.query(Logic)
        .filter(Logic.id == logic_id, Logic.is_deleted.is_(False))
        .first()
    )


def delete_logic_query(db: Session, logic: Logic) -> None:
    """
    Query to delete a logic (soft delete).
    """
    setattr(logic, "is_deleted", True)
    db.commit()
