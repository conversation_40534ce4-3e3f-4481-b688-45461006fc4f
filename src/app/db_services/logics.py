from typing import Dict, List, Optional
from uuid import UUID

from entities.customers import Customer
from entities.logics import Logic
from sqlalchemy.orm import Session, joinedload


def get_logic_by_id_query(db: Session, logic_id: UUID) -> Optional[Logic]:
    """
    Query to fetch a logic by ID.
    """
    return (
        db.query(Logic)
        .filter(Logic.id == logic_id, Logic.is_deleted.is_(False))
        .first()
    )


def get_logic_by_id_with_relationships_query(
    db: Session, logic_id: UUID
) -> Optional[Logic]:
    """
    Query to fetch a logic by ID with all relationships loaded.
    """
    return (
        db.query(Logic)
        .options(
            joinedload(Logic.customers),
            joinedload(Logic.demands),
            joinedload(Logic.market_related_data),
            joinedload(Logic.solar_and_other_re),
            joinedload(Logic.interstate_transmission_charges),
            joinedload(Logic.intrastate_transmission_charges),
            joinedload(Logic.wheeling_charges),
            joinedload(Logic.cross_subsidy_surcharges),
            joinedload(Logic.additional_surcharges),
            joinedload(Logic.scheduling_and_other_charges),
            joinedload(Logic.iex_transaction_fees),
            joinedload(Logic.rec_charges),
            joinedload(Logic.dsm_charges),
            joinedload(Logic.tod_time_blocks),
            joinedload(Logic.tod_base_discom_tariffs),
            joinedload(Logic.tod_rebates),
            joinedload(Logic.tod_fuel_surcharges),
            joinedload(Logic.tod_total_discom_landed_tariffs),
            joinedload(Logic.inter_state_transmission_losses),
            joinedload(Logic.intra_state_transmission_losses),
            joinedload(Logic.wheeling_losses),
            joinedload(Logic.customer_and_state),
            joinedload(Logic.settlements),
            joinedload(Logic.conditions),
            joinedload(Logic.deployments),
            joinedload(Logic.forward_testings),
            joinedload(Logic.backward_testings),
        )
        .filter(Logic.id == logic_id, Logic.is_deleted.is_(False))
        .first()
    )


def create_logic_query(db: Session, logic: Logic) -> Logic:
    """
    Create logic in database with auto-generated logic_id.
    """
    db.add(logic)
    db.commit()
    db.refresh(logic)
    return logic


def update_logic_query(
    db: Session, logic: Logic, update_data: Optional[Dict] = None
) -> Logic:
    """
    Query to update an existing logic.
    """
    if update_data:
        for field, value in update_data.items():
            if hasattr(logic, field):
                setattr(logic, field, value)

    db.commit()
    db.refresh(logic)
    return logic


def delete_logic_query(db: Session, logic: Logic) -> None:
    """
    Query to delete a logic (soft delete).
    """
    setattr(logic, "is_deleted", True)
    db.commit()


def get_customers_by_ids_query(db: Session, customer_ids: List[int]) -> List[Customer]:
    """
    Query to fetch customers by their IDs.
    """
    return (
        db.query(Customer)
        .filter(Customer.id.in_(customer_ids), Customer.is_deleted.is_(False))
        .all()
    )


def create_charge_entities(
    db: Session, logic_id: UUID, charge_data: List[Dict], entity_class
) -> List:
    """
    Helper function to create charge-type entities (interstate, intrastate, wheeling, etc.)
    """
    entities = []
    for data in charge_data:
        entity = entity_class(logic_id=logic_id, **data)
        entities.append(entity)
        db.add(entity)
    return entities


def create_loss_entities(
    db: Session, logic_id: UUID, loss_data: List[Dict], entity_class
) -> List:
    """
    Helper function to create loss-type entities
    """
    entities = []
    for data in loss_data:
        entity = entity_class(logic_id=logic_id, **data)
        entities.append(entity)
        db.add(entity)
    return entities


def create_tod_entities(
    db: Session, logic_id: UUID, tod_data: List[Dict], entity_class
) -> List:
    """
    Helper function to create TOD-type entities
    """
    entities = []
    for data in tod_data:
        entity = entity_class(logic_id=logic_id, **data)
        entities.append(entity)
        db.add(entity)
    return entities


def create_generic_entities(
    db: Session, logic_id: UUID, entity_data: List[Dict], entity_class
) -> List:
    """
    Helper function to create generic entities with logic_id
    """
    entities = []
    for data in entity_data:
        entity = entity_class(logic_id=logic_id, **data)
        entities.append(entity)
        db.add(entity)
    return entities
