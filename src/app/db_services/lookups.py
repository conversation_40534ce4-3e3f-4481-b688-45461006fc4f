from typing import List

from database.core import DbSession
from entities.lookup_tables import (
    Carry_forwards,
    ConditionType,
    Factor,
    MarketType,
    Ratio_value,
    RenewableEnergyType,
    SettlementType,
)
from entities.states import State


def get_all_states(db: DbSession) -> List[State]:
    """
    Get all states from the database.
    """
    return db.query(State).order_by(State.id).all()


def get_all_market_types(db: DbSession) -> List[MarketType]:
    """
    Get all market types from market_types lookup table.
    """
    return db.query(MarketType).order_by(MarketType.id).all()


def get_all_renewable_energy_types(db: DbSession) -> List[RenewableEnergyType]:
    """
    Get all renewable energy types from renewable_energy_types lookup table.
    """
    return db.query(RenewableEnergyType).order_by(RenewableEnergyType.id).all()


def get_all_settlement_types(db: DbSession) -> List[SettlementType]:
    """
    Get all settlement types from settlement_types lookup table.
    """
    return db.query(SettlementType).order_by(SettlementType.id).all()


def get_all_conditions(db: DbSession) -> List[ConditionType]:
    """
    Get all conditions from condition_types lookup table.
    """
    return db.query(ConditionType).order_by(ConditionType.id).all()


def get_all_ratio_values(db: DbSession) -> List[Ratio_value]:
    """
    Get all ratio values from ratio_values lookup table.
    """
    return db.query(Ratio_value).order_by(Ratio_value.id).all()


def get_all_factors(db: DbSession) -> List[Factor]:
    """
    Get all factors from factors lookup table.
    """
    return db.query(Factor).order_by(Factor.id).all()


def get_all_carry_forwards(db: DbSession) -> List[Carry_forwards]:
    """
    Get all carry forward values from carry_forwards lookup table.
    """
    return db.query(Carry_forwards).order_by(Carry_forwards.id).all()
