import os
import sys
import uuid
from datetime import date, datetime, timezone
from unittest.mock import Mock

import pytest
from fastapi.testclient import TestClient
from passlib.context import CryptContext

sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), "app"))

# Set environment variables for testing
os.environ["SKIP_DB_INIT"] = "true"
os.environ["DATABASE_URL"] = "sqlite:///:memory:"
os.environ["SECRET_KEY"] = "test-secret-key-for-testing-only-not-for-production-use"
os.environ["ALGORITHM"] = "HS256"
os.environ["ACCESS_TOKEN_EXPIRE_MINUTES"] = "30"
os.environ["REFRESH_TOKEN_EXPIRE_DAYS"] = "7"

from database.core import get_db
from entities.customers import Customer
from entities.logics import Logic
from entities.states import State
from entities.users import User
from main import app
from routes.v1.auth.service import get_current_user


@pytest.fixture
def mock_db_session():
    """Create a mock database session for unit tests."""
    mock_session = Mock()
    mock_session.add = Mock()
    mock_session.commit = Mock()
    mock_session.rollback = Mock()
    mock_session.refresh = Mock()
    mock_session.query = Mock()
    return mock_session


@pytest.fixture
def client(mock_db_session, mock_current_user):
    """Create a test client for API testing with mocked database and auth."""

    def override_get_db():
        return mock_db_session

    def override_get_current_user():
        return mock_current_user

    app.dependency_overrides[get_db] = override_get_db
    app.dependency_overrides[get_current_user] = override_get_current_user

    with TestClient(app) as test_client:
        yield test_client

    # Clean up dependency overrides
    app.dependency_overrides.clear()


@pytest.fixture
def bcrypt_context():
    """Provide bcrypt context for password hashing."""
    return CryptContext(schemes=["bcrypt"], deprecated="auto")


@pytest.fixture
def sample_user_data():
    """Sample user data for testing."""
    return {
        "first_name": "John",
        "last_name": "Doe",
        "email": "<EMAIL>",
        "password": "TestPassword123!",
    }


@pytest.fixture
def sample_user(bcrypt_context, sample_user_data) -> User:
    """Create a sample user mock."""
    user = User(
        id=uuid.uuid4(),
        first_name=sample_user_data["first_name"],
        last_name=sample_user_data["last_name"],
        email=sample_user_data["email"],
        password=bcrypt_context.hash(sample_user_data["password"]),
        role="admin",
        is_active=True,
    )
    return user


@pytest.fixture
def sample_states() -> list[State]:
    """Create sample states mocks."""
    states = [
        State(id=1, name="California"),
        State(id=2, name="Texas"),
        State(id=3, name="New York"),
    ]
    return states


@pytest.fixture
def sample_customer_data():
    """Sample customer data for testing."""
    return {
        "customer_name": "Test Customer Inc.",
        "onboarded_date": date(2024, 1, 15),
        "state_ids": [1, 2],
    }


@pytest.fixture
def sample_customer(sample_user, sample_states, sample_customer_data) -> Customer:
    """Create a sample customer mock."""
    customer = Customer(
        id=1,
        customer_name=sample_customer_data["customer_name"],
        onboarded_date=sample_customer_data["onboarded_date"],
        created_by=sample_user.id,
        updated_by=sample_user.id,
        states=[sample_states[0], sample_states[1]],  # First two states
    )
    customer.customer_id = "EUWI-0001"
    customer.is_deleted = False
    customer.created_at = datetime.now(timezone.utc)
    customer.updated_at = datetime.now(timezone.utc)
    return customer


@pytest.fixture
def multiple_customers(sample_user, sample_states) -> list[Customer]:
    """Create multiple customers for pagination testing."""
    customers = []
    for i in range(5):
        customer = Customer(
            id=i + 1,
            customer_name=f"Customer {i + 1}",
            onboarded_date=date(2024, 1, i + 1),
            created_by=sample_user.id,
            updated_by=sample_user.id,
            states=[sample_states[i % len(sample_states)]],
        )
        customer.customer_id = f"EUWI-{i + 1:04d}"
        customers.append(customer)
    return customers


@pytest.fixture
def mock_current_user(sample_user):
    """Mock current user for dependency injection."""
    mock_user = Mock()
    mock_user.user_id = sample_user.id
    mock_user.email = sample_user.email
    return mock_user


@pytest.fixture
def sample_logic(sample_user, sample_logic_data) -> Logic:
    """Create a sample logic mock."""
    logic = Logic(
        id=uuid.uuid4(),
        is_draft=True,
        is_deleted=False,
        created_by=sample_user.id,
        updated_by=sample_user.id,
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc),
    )
    logic.logic_id = "LOGIC-12345678"
    return logic


@pytest.fixture
def auth_headers(sample_user):
    """Create authentication headers for API requests."""
    # This would normally create a real JWT token, but for unit tests
    # we'll mock the authentication dependency instead
    return {"Authorization": f"Bearer mock-token-{sample_user.id}"}


@pytest.fixture
def mock_logger():
    """Mock logger for testing."""
    return Mock()


def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line("markers", "unit: Unit tests")
    config.addinivalue_line("markers", "integration: Integration tests")
    config.addinivalue_line("markers", "auth: Authentication related tests")
    config.addinivalue_line("markers", "customer: Customer related tests")
    config.addinivalue_line("markers", "service: Service layer tests")
    config.addinivalue_line("markers", "controller: Controller layer tests")
