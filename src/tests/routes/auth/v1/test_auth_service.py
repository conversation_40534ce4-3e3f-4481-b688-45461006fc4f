import uuid
from datetime import datetime, timedelta, timezone
from unittest.mock import Mo<PERSON>, patch

import jwt
import pytest
from fastapi import HTT<PERSON><PERSON>x<PERSON>, status
from fastapi.security import OAuth2PasswordRequestForm
from routes.v1.auth import service
from routes.v1.auth.models import (
    CreateUserRequest,
    ForgotPasswordRequest,
    RefreshTokenRequest,
    ResetPassword,
    Token,
    TokenData,
    UserResponse,
)
from sqlalchemy.exc import IntegrityError
from utils.exceptions import AuthenticationError


@pytest.mark.unit
@pytest.mark.auth
@pytest.mark.service
class TestPasswordFunctions:
    """Test password hashing and verification functions."""

    def test_get_password_hash(self):
        """Test password hashing."""
        password = "TestPassword123!"
        hashed = service.get_password_hash(password)

        assert hashed != password
        assert len(hashed) > 0
        assert hashed.startswith("$2b$")

    def test_verify_password_correct(self):
        """Test password verification with correct password."""
        password = "TestPassword123!"
        hashed = service.get_password_hash(password)

        assert service.verify_password(password, hashed) is True

    def test_verify_password_incorrect(self):
        """Test password verification with incorrect password."""
        password = "TestPassword123!"
        wrong_password = "WrongPassword123!"
        hashed = service.get_password_hash(password)

        assert service.verify_password(wrong_password, hashed) is False


@pytest.mark.unit
@pytest.mark.auth
@pytest.mark.service
class TestUserAuthentication:
    """Test user authentication functions."""

    @patch("routes.v1.auth.service.users_db_service.get_user_by_email")
    def test_authenticate_user_success(
        self, mock_get_user, mock_db_session, sample_user, sample_user_data
    ):
        """Test successful user authentication."""
        mock_get_user.return_value = sample_user

        result = service.authenticate_user(
            sample_user_data["email"], sample_user_data["password"], mock_db_session
        )

        assert result is not False
        assert result.email == sample_user_data["email"]
        assert result.id == sample_user.id

    @patch("routes.v1.auth.service.users_db_service.get_user_by_email")
    def test_authenticate_user_wrong_password(
        self, mock_get_user, mock_db_session, sample_user, sample_user_data
    ):
        """Test authentication with wrong password."""
        mock_get_user.return_value = sample_user

        result = service.authenticate_user(
            sample_user_data["email"], "WrongPassword123!", mock_db_session
        )

        assert result is False

    @patch("routes.v1.auth.service.users_db_service.get_user_by_email")
    def test_authenticate_user_nonexistent_email(self, mock_get_user, mock_db_session):
        """Test authentication with non-existent email."""
        mock_get_user.return_value = None

        result = service.authenticate_user(
            "<EMAIL>", "TestPassword123!", mock_db_session
        )

        assert result is False


@pytest.mark.unit
@pytest.mark.auth
@pytest.mark.service
class TestUserCreation:
    """Test user creation functionality."""

    @patch("routes.v1.auth.service.users_db_service.create_user")
    def test_create_user_success(self, mock_create_user, mock_db_session):
        """Test successful user creation."""
        # Mock the database service to return a user with all required fields
        mock_user = Mock()
        mock_user.id = uuid.uuid4()
        mock_user.email = "<EMAIL>"
        mock_user.first_name = "Jane"
        mock_user.last_name = "Smith"
        mock_user.role = "admin"
        mock_user.is_active = True
        mock_user.created_at = datetime.now(timezone.utc)
        mock_user.updated_at = datetime.now(timezone.utc)
        mock_create_user.return_value = mock_user

        create_request = CreateUserRequest(
            first_name="Jane",
            last_name="Smith",
            email="<EMAIL>",
            password="TestPassword123!",
        )

        result = service.create_user(create_request, mock_db_session)

        assert isinstance(result, UserResponse)
        assert result.email == create_request.email
        assert result.first_name == create_request.first_name
        assert result.last_name == create_request.last_name
        assert result.is_active is True

    @patch("routes.v1.auth.service.users_db_service.create_user")
    def test_create_user_duplicate_email(
        self, mock_create_user, mock_db_session, sample_user_data
    ):
        """Test user creation with duplicate email."""
        mock_create_user.side_effect = IntegrityError("", "", "")

        create_request = CreateUserRequest(
            first_name="Another",
            last_name="User",
            email=sample_user_data["email"],  # Same email as existing user
            password="TestPassword123!",
        )

        with pytest.raises(HTTPException) as exc_info:
            service.create_user(create_request, mock_db_session)

        assert exc_info.value.status_code == status.HTTP_409_CONFLICT
        assert "already exists" in exc_info.value.detail

    @patch("routes.v1.auth.service.users_db_service.create_user")
    def test_create_user_database_error(self, mock_create_user, mock_db_session):
        """Test user creation with database error."""
        mock_create_user.side_effect = Exception("Database error")

        create_request = CreateUserRequest(
            first_name="Test",
            last_name="User",
            email="<EMAIL>",
            password="TestPassword123!",
        )

        with pytest.raises(Exception):
            service.create_user(create_request, mock_db_session)


@pytest.mark.unit
@pytest.mark.auth
@pytest.mark.service
class TestTokenCreation:
    """Test JWT token creation functions."""

    def test_create_access_token(self, sample_user):
        """Test access token creation."""
        expires_delta = timedelta(minutes=30)
        token = service.create_access_token(
            sample_user.email, sample_user.id, expires_delta
        )

        assert isinstance(token, str)
        assert len(token) > 0

        # Decode and verify token content
        payload = jwt.decode(token, service.SECRET_KEY, algorithms=[service.ALGORITHM])
        assert payload["sub"] == sample_user.email
        assert payload["id"] == str(sample_user.id)
        assert payload["type"] == "access"

    def test_create_refresh_token(self, sample_user):
        """Test refresh token creation."""
        expires_delta = timedelta(days=7)
        token = service.create_refresh_token(
            sample_user.email, sample_user.id, expires_delta
        )

        assert isinstance(token, str)
        assert len(token) > 0

        # Decode and verify token content
        payload = jwt.decode(token, service.SECRET_KEY, algorithms=[service.ALGORITHM])
        assert payload["sub"] == sample_user.email
        assert payload["id"] == str(sample_user.id)
        assert payload["type"] == "refresh"


@pytest.mark.unit
@pytest.mark.auth
@pytest.mark.service
class TestLoginForAccessToken:
    """Test login functionality."""

    @patch("routes.v1.auth.service.authenticate_user")
    def test_login_success(
        self, mock_authenticate, mock_db_session, sample_user, sample_user_data
    ):
        """Test successful login."""
        mock_authenticate.return_value = sample_user

        form_data = OAuth2PasswordRequestForm(
            username=sample_user_data["email"], password=sample_user_data["password"]
        )

        result = service.login_for_access_token(form_data, mock_db_session)

        assert isinstance(result, Token)
        assert result.token_type == "bearer"
        assert len(result.access_token) > 0
        assert len(result.refresh_token) > 0

    @patch("routes.v1.auth.service.authenticate_user")
    def test_login_invalid_credentials(
        self, mock_authenticate, mock_db_session, sample_user
    ):
        """Test login with invalid credentials."""
        mock_authenticate.return_value = False

        form_data = OAuth2PasswordRequestForm(
            username=sample_user.email, password="WrongPassword123!"
        )

        with pytest.raises(AuthenticationError):
            service.login_for_access_token(form_data, mock_db_session)

    @patch("routes.v1.auth.service.authenticate_user")
    def test_login_nonexistent_user(self, mock_authenticate, mock_db_session):
        """Test login with non-existent user."""
        mock_authenticate.return_value = False

        form_data = OAuth2PasswordRequestForm(
            username="<EMAIL>", password="TestPassword123!"
        )

        with pytest.raises(AuthenticationError):
            service.login_for_access_token(form_data, mock_db_session)


@pytest.mark.unit
@pytest.mark.auth
@pytest.mark.service
class TestTokenVerification:
    """Test token verification functions."""

    def test_verify_token_valid_access_token(self, sample_user):
        """Test verification of valid access token."""
        token = service.create_access_token(
            sample_user.email, sample_user.id, timedelta(minutes=30)
        )

        result = service.verify_token(token, "access")

        assert isinstance(result, TokenData)
        assert result.user_id == str(sample_user.id)

    def test_verify_token_wrong_type(self, sample_user):
        """Test verification with wrong token type."""
        token = service.create_access_token(
            sample_user.email, sample_user.id, timedelta(minutes=30)
        )

        with pytest.raises(AuthenticationError):
            service.verify_token(token, "refresh")

    def test_verify_token_invalid_token(self):
        """Test verification of invalid token."""
        with pytest.raises(AuthenticationError):
            service.verify_token("invalid-token", "access")

    def test_verify_token_expired_token(self, sample_user):
        """Test verification of expired token."""
        # Create token that expires immediately
        token = service.create_access_token(
            sample_user.email,
            sample_user.id,
            timedelta(seconds=-1),  # Already expired
        )

        with pytest.raises(AuthenticationError):
            service.verify_token(token, "access")

    def test_verify_refresh_token_valid(self, sample_user):
        """Test verification of valid refresh token."""
        token = service.create_refresh_token(
            sample_user.email, sample_user.id, timedelta(days=7)
        )

        result = service.verify_refresh_token(token, "refresh")

        assert result is True

    def test_verify_refresh_token_wrong_type(self, sample_user):
        """Test refresh token verification with wrong type."""
        token = service.create_refresh_token(
            sample_user.email, sample_user.id, timedelta(days=7)
        )

        with pytest.raises(AuthenticationError):
            service.verify_refresh_token(token, "access")

    def test_verify_refresh_token_invalid(self):
        """Test verification of invalid refresh token."""
        with pytest.raises(AuthenticationError):
            service.verify_refresh_token("invalid-token", "refresh")


@pytest.mark.unit
@pytest.mark.auth
@pytest.mark.service
class TestRefreshAccessToken:
    """Test token refresh functionality."""

    @patch("routes.v1.auth.service.users_db_service.get_user_by_id")
    @patch("routes.v1.auth.service.jwt.decode")
    def test_refresh_access_token_success(
        self, mock_jwt_decode, mock_get_user, mock_db_session, sample_user
    ):
        """Test successful token refresh."""
        mock_get_user.return_value = sample_user

        # Mock JWT decode to return valid payload - use integer ID as expected by service
        mock_jwt_decode.return_value = {
            "id": 1,  # Use integer ID instead of UUID string
            "type": "refresh",
            "email": sample_user.email,
            "exp": (datetime.now(timezone.utc) + timedelta(days=7)).timestamp(),
        }

        refresh_request = RefreshTokenRequest(refresh_token="valid-refresh-token")
        result = service.refresh_access_token(refresh_request, mock_db_session)

        assert isinstance(result, Token)
        assert result.token_type == "bearer"
        assert len(result.access_token) > 0
        assert len(result.refresh_token) > 0

    def test_refresh_access_token_invalid_token(self, mock_db_session):
        """Test token refresh with invalid token."""
        refresh_request = RefreshTokenRequest(refresh_token="invalid-token")

        with pytest.raises(AuthenticationError):
            service.refresh_access_token(refresh_request, mock_db_session)

    def test_refresh_access_token_wrong_type(self, mock_db_session, sample_user):
        """Test token refresh with access token instead of refresh token."""
        access_token = service.create_access_token(
            sample_user.email, sample_user.id, timedelta(minutes=30)
        )

        refresh_request = RefreshTokenRequest(refresh_token=access_token)

        with pytest.raises(AuthenticationError):
            service.refresh_access_token(refresh_request, mock_db_session)

    @patch("routes.v1.auth.service.users_db_service.get_user_by_id")
    def test_refresh_access_token_user_not_found(
        self, mock_get_user, mock_db_session, sample_user
    ):
        """Test token refresh when user no longer exists."""
        mock_get_user.return_value = None

        refresh_token = service.create_refresh_token(
            sample_user.email, sample_user.id, timedelta(days=7)
        )

        refresh_request = RefreshTokenRequest(refresh_token=refresh_token)

        with pytest.raises(AuthenticationError):
            service.refresh_access_token(refresh_request, mock_db_session)


@pytest.mark.unit
@pytest.mark.auth
@pytest.mark.service
class TestPasswordReset:
    """Test password reset functionality."""

    def test_generate_reset_token(self):
        """Test password reset token generation."""
        email = "<EMAIL>"
        token = service.generate_reset_token(email)

        assert isinstance(token, str)
        assert len(token) > 0

        # Verify token content
        payload = jwt.decode(token, service.SECRET_KEY, algorithms=[service.ALGORITHM])
        assert payload["email"] == email
        assert payload["type"] == "password_reset"

    def test_verify_reset_token_valid(self):
        """Test verification of valid reset token."""
        email = "<EMAIL>"
        token = service.generate_reset_token(email)

        result = service.verify_reset_token(token)

        assert result == email

    def test_verify_reset_token_invalid(self):
        """Test verification of invalid reset token."""
        with pytest.raises(HTTPException) as exc_info:
            service.verify_reset_token("invalid-token")

        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST

    def test_verify_reset_token_expired(self):
        """Test verification of expired reset token."""
        # Create token with past expiration
        payload = {
            "email": "<EMAIL>",
            "exp": datetime.now(timezone.utc) - timedelta(hours=1),
            "type": "password_reset",
        }
        expired_token = jwt.encode(
            payload, service.SECRET_KEY, algorithm=service.ALGORITHM
        )

        with pytest.raises(HTTPException) as exc_info:
            service.verify_reset_token(expired_token)

        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "expired" in exc_info.value.detail

    @patch("routes.v1.auth.service.users_db_service.get_user_by_email")
    def test_forgot_password_existing_user(
        self, mock_get_user, mock_db_session, sample_user
    ):
        """Test forgot password for existing user."""
        mock_get_user.return_value = sample_user

        request = ForgotPasswordRequest(email=sample_user.email)
        mock_request = Mock()

        result = service.forgot_password(request, mock_request, mock_db_session)

        assert "message" in result
        assert "token" in result
        assert len(result["token"]) > 0

    @patch("routes.v1.auth.service.users_db_service.get_user_by_email")
    def test_forgot_password_nonexistent_user(self, mock_get_user, mock_db_session):
        """Test forgot password for non-existent user."""
        mock_get_user.return_value = None

        request = ForgotPasswordRequest(email="<EMAIL>")
        mock_request = Mock()

        result = service.forgot_password(request, mock_request, mock_db_session)

        # Should still return success for security reasons
        assert "message" in result
        assert "token" in result

    @patch("routes.v1.auth.service.users_db_service.get_user_by_email")
    @patch("routes.v1.auth.service.users_db_service.update_user")
    def test_reset_password_success(
        self, mock_update_user, mock_get_user, mock_db_session, sample_user
    ):
        """Test successful password reset."""
        mock_get_user.return_value = sample_user
        mock_update_user.return_value = sample_user

        new_password = "NewPassword123!"
        reset_token = service.generate_reset_token(sample_user.email)

        reset_request = ResetPassword(
            reset_token=reset_token,
            new_password=new_password,
            confirm_password=new_password,
        )

        result = service.reset_password(reset_request, mock_db_session)

        assert "message" in result
        assert "successful" in result["message"]

    def test_reset_password_invalid_token(self, mock_db_session):
        """Test password reset with invalid token."""
        reset_request = ResetPassword(
            reset_token="invalid-token",
            new_password="NewPassword123!",
            confirm_password="NewPassword123!",
        )

        with pytest.raises(HTTPException):
            service.reset_password(reset_request, mock_db_session)

    @patch("routes.v1.auth.service.users_db_service.get_user_by_email")
    def test_reset_password_user_not_found(self, mock_get_user, mock_db_session):
        """Test password reset when user not found."""
        mock_get_user.return_value = None

        reset_token = service.generate_reset_token("<EMAIL>")
        reset_request = ResetPassword(
            reset_token=reset_token,
            new_password="NewPassword123!",
            confirm_password="NewPassword123!",
        )

        with pytest.raises(HTTPException) as exc_info:
            service.reset_password(reset_request, mock_db_session)

        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
