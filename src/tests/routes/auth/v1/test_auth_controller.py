from unittest.mock import patch

import pytest
from fastapi import HTTPEx<PERSON>, status
from routes.v1.auth.models import (
    Token,
    UserResponse,
)
from utils.exceptions import AuthenticationError


@pytest.mark.unit
@pytest.mark.auth
@pytest.mark.controller
class TestAuthControllerRegistration:
    """Test user registration endpoint."""

    @patch("routes.v1.auth.service.create_user")
    def test_create_user_success(self, mock_create_user, client):
        """Test successful user registration."""
        # Mock the service response
        mock_user_response = UserResponse(
            id="123e4567-e89b-12d3-a456-426614174000",
            email="<EMAIL>",
            first_name="<PERSON>",
            last_name="<PERSON><PERSON>",
            is_active=True,
        )
        mock_create_user.return_value = mock_user_response

        # Test data
        user_data = {
            "first_name": "<PERSON>",
            "last_name": "<PERSON><PERSON>",
            "email": "<EMAIL>",
            "password": "TestPassword123!",
        }

        # Make request
        response = client.post("/v1/auth/register", json=user_data)

        # Assertions
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["email"] == user_data["email"]
        assert response_data["first_name"] == user_data["first_name"]
        assert response_data["last_name"] == user_data["last_name"]
        assert response_data["is_active"] is True

        # Verify service was called correctly
        mock_create_user.assert_called_once()
        call_args = mock_create_user.call_args[0]
        assert call_args[0].email == user_data["email"]

    @patch("routes.v1.auth.service.create_user")
    def test_create_user_duplicate_email(self, mock_create_user, client):
        """Test registration with duplicate email."""
        # Mock service to raise conflict exception
        mock_create_user.side_effect = HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="User with this email already exists",
        )

        user_data = {
            "first_name": "John",
            "last_name": "Doe",
            "email": "<EMAIL>",
            "password": "TestPassword123!",
        }

        response = client.post("/v1/auth/register", json=user_data)

        assert response.status_code == 409
        assert "already exists" in response.json()["detail"]

    def test_create_user_invalid_data(self, client):
        """Test registration with invalid data."""
        # Missing required fields
        user_data = {
            "first_name": "John",
            # Missing last_name, email, password
        }

        response = client.post("/v1/auth/register", json=user_data)

        assert response.status_code == 422  # Validation error

    def test_create_user_weak_password(self, client):
        """Test registration with weak password."""
        user_data = {
            "first_name": "John",
            "last_name": "Doe",
            "email": "<EMAIL>",
            "password": "weak",  # Too weak
        }

        response = client.post("/v1/auth/register", json=user_data)

        assert response.status_code == 422  # Validation error

    @patch("routes.v1.auth.service.create_user")
    def test_create_user_service_error(self, mock_create_user, client):
        """Test registration with service error."""
        mock_create_user.side_effect = Exception("Database error")

        user_data = {
            "first_name": "John",
            "last_name": "Doe",
            "email": "<EMAIL>",
            "password": "TestPassword123!",
        }

        # The controller catches the exception and re-raises it, so FastAPI will return 500
        with pytest.raises(Exception, match="Database error"):
            response = client.post("/v1/auth/register", json=user_data)


@pytest.mark.unit
@pytest.mark.auth
@pytest.mark.controller
class TestAuthControllerLogin:
    """Test user login endpoint."""

    @patch("routes.v1.auth.service.login_for_access_token")
    def test_login_success(self, mock_login, client):
        """Test successful login."""
        # Mock the service response
        mock_token = Token(
            access_token="mock-access-token",
            refresh_token="mock-refresh-token",
            token_type="bearer",
        )
        mock_login.return_value = mock_token

        # Login data
        login_data = {"username": "<EMAIL>", "password": "TestPassword123!"}

        response = client.post("/v1/auth/login", data=login_data)

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["access_token"] == "mock-access-token"
        assert response_data["refresh_token"] == "mock-refresh-token"
        assert response_data["token_type"] == "bearer"

    @patch("routes.v1.auth.service.login_for_access_token")
    def test_login_invalid_credentials(self, mock_login, client):
        """Test login with invalid credentials."""
        mock_login.side_effect = AuthenticationError()

        login_data = {"username": "<EMAIL>", "password": "WrongPassword"}

        response = client.post("/v1/auth/login", data=login_data)

        assert response.status_code == 401

    def test_login_missing_data(self, client):
        """Test login with missing data."""
        response = client.post("/v1/auth/login", data={})

        assert response.status_code == 422  # Validation error


@pytest.mark.unit
@pytest.mark.auth
@pytest.mark.controller
class TestAuthControllerTokenRefresh:
    """Test token refresh endpoint."""

    @patch("routes.v1.auth.service.refresh_access_token")
    def test_refresh_token_success(self, mock_refresh, client):
        """Test successful token refresh."""
        # Mock the service response
        mock_token = Token(
            access_token="new-access-token",
            refresh_token="new-refresh-token",
            token_type="bearer",
        )
        mock_refresh.return_value = mock_token

        refresh_data = {"refresh_token": "valid-refresh-token"}

        response = client.post("/v1/auth/refresh-token", json=refresh_data)

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["access_token"] == "new-access-token"
        assert response_data["refresh_token"] == "new-refresh-token"
        assert response_data["token_type"] == "bearer"

    @patch("routes.v1.auth.service.refresh_access_token")
    def test_refresh_token_invalid(self, mock_refresh, client):
        """Test token refresh with invalid token."""
        mock_refresh.side_effect = AuthenticationError("Invalid refresh token")

        refresh_data = {"refresh_token": "invalid-refresh-token"}

        response = client.post("/v1/auth/refresh-token", json=refresh_data)

        assert response.status_code == 401

    def test_refresh_token_missing_data(self, client):
        """Test token refresh with missing data."""
        response = client.post("/v1/auth/refresh-token", json={})

        assert response.status_code == 422  # Validation error


@pytest.mark.unit
@pytest.mark.auth
@pytest.mark.controller
class TestAuthControllerForgotPassword:
    """Test forgot password endpoint."""

    @patch("routes.v1.auth.service.forgot_password")
    def test_forgot_password_success(self, mock_forgot, client):
        """Test successful forgot password request."""
        mock_forgot.return_value = {
            "message": "Forgot password request processed",
            "token": "reset-token-123",
        }

        forgot_data = {"email": "<EMAIL>"}

        response = client.post("/v1/auth/forgot-password", json=forgot_data)

        assert response.status_code == 200
        response_data = response.json()
        assert "message" in response_data
        assert "token" in response_data

    @patch("routes.v1.auth.service.forgot_password")
    def test_forgot_password_service_error(self, mock_forgot, client):
        """Test forgot password with service error."""
        mock_forgot.side_effect = Exception("Service error")

        forgot_data = {"email": "<EMAIL>"}

        with pytest.raises(Exception, match="Service error"):
            response = client.post("/v1/auth/forgot-password", json=forgot_data)

    def test_forgot_password_invalid_email(self, client):
        """Test forgot password with invalid email."""
        forgot_data = {"email": "invalid-email"}

        response = client.post("/v1/auth/forgot-password", json=forgot_data)

        assert response.status_code == 422  # Validation error


@pytest.mark.unit
@pytest.mark.auth
@pytest.mark.controller
class TestAuthControllerResetPassword:
    """Test password reset endpoint."""

    @patch("routes.v1.auth.service.reset_password")
    def test_reset_password_success(self, mock_reset, client):
        """Test successful password reset."""
        mock_reset.return_value = {"message": "Password reset successful"}

        reset_data = {
            "reset_token": "valid-reset-token",
            "new_password": "NewPassword123!",
            "confirm_password": "NewPassword123!",
        }

        response = client.post("/v1/auth/reset-password", json=reset_data)

        assert response.status_code == 200
        response_data = response.json()
        assert "message" in response_data
        assert "successful" in response_data["message"]

    @patch("routes.v1.auth.service.reset_password")
    def test_reset_password_invalid_token(self, mock_reset, client):
        """Test password reset with invalid token."""
        mock_reset.side_effect = HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid reset token"
        )

        reset_data = {
            "reset_token": "invalid-token",
            "new_password": "NewPassword123!",
            "confirm_password": "NewPassword123!",
        }

        response = client.post("/v1/auth/reset-password", json=reset_data)

        assert response.status_code == 400
        assert "Invalid reset token" in response.json()["detail"]

    def test_reset_password_mismatched_passwords(self, client):
        """Test password reset with mismatched passwords."""
        reset_data = {
            "reset_token": "valid-token",
            "new_password": "NewPassword123!",
            "confirm_password": "DifferentPassword123!",
        }

        response = client.post("/v1/auth/reset-password", json=reset_data)

        assert response.status_code == 422  # Validation error

    def test_reset_password_weak_password(self, client):
        """Test password reset with weak password."""
        reset_data = {
            "reset_token": "valid-token",
            "new_password": "weak",
            "confirm_password": "weak",
        }

        response = client.post("/v1/auth/reset-password", json=reset_data)

        assert response.status_code == 422  # Validation error

    @patch("routes.v1.auth.service.reset_password")
    def test_reset_password_service_error(self, mock_reset, client):
        """Test password reset with service error."""
        mock_reset.side_effect = Exception("Service error")

        reset_data = {
            "reset_token": "valid-token",
            "new_password": "NewPassword123!",
            "confirm_password": "NewPassword123!",
        }

        with pytest.raises(Exception, match="Service error"):
            response = client.post("/v1/auth/reset-password", json=reset_data)
