"""
Test cases for lookups controller functionality.
"""

from unittest.mock import Mock, patch

import pytest
from fastapi import HTTP<PERSON>x<PERSON>, status


def create_mock_objects(data_list, attribute_name):
    """Helper function to create mock objects with specified attributes."""
    mock_objects = []
    for i, value in enumerate(data_list, 1):
        mock_obj = Mock()
        mock_obj.id = i
        setattr(mock_obj, attribute_name, value)
        mock_objects.append(mock_obj)
    return mock_objects


@pytest.mark.unit
@pytest.mark.controller
class TestLookupsControllerStates:
    """Test states lookup endpoint."""

    @patch("routes.v1.lookups.service.get_all_states")
    def test_get_states_success(self, mock_service, client, mock_current_user):
        """Test successful states retrieval."""
        mock_states = create_mock_objects(["California", "Texas", "New York"], "name")
        mock_service.return_value = mock_states

        response = client.get("/v1/lookups/states")

        assert response.status_code == 200
        response_data = response.json()
        assert "states" in response_data
        assert len(response_data["states"]) == 3
        assert response_data["states"][0]["name"] == "California"
        mock_service.assert_called_once()

    @patch("routes.v1.lookups.service.get_all_states")
    def test_get_states_empty_result(self, mock_service, client, mock_current_user):
        """Test states retrieval with empty result."""
        mock_service.return_value = []

        response = client.get("/v1/lookups/states")

        assert response.status_code == 200
        response_data = response.json()
        assert "states" in response_data
        assert len(response_data["states"]) == 0

    @patch("routes.v1.lookups.service.get_all_states")
    def test_get_states_service_error(self, mock_service, client, mock_current_user):
        """Test states retrieval with service error."""
        mock_service.side_effect = Exception("Database error")

        response = client.get("/v1/lookups/states")

        assert response.status_code == 500
        response_data = response.json()
        assert response_data["detail"] == "Error retrieving states"


@pytest.mark.unit
@pytest.mark.controller
class TestLookupsControllerMarketTypes:
    """Test market types lookup endpoint."""

    @patch("routes.v1.lookups.service.get_all_market_types")
    def test_get_market_types_success(self, mock_service, client, mock_current_user):
        """Test successful market types retrieval."""
        mock_market_types = create_mock_objects(
            ["Day-Ahead", "Real-Time", "Capacity"], "market_type"
        )
        mock_service.return_value = mock_market_types

        response = client.get("/v1/lookups/market_types")

        assert response.status_code == 200
        response_data = response.json()
        assert "market_types" in response_data
        assert len(response_data["market_types"]) == 3
        assert response_data["market_types"][0]["market_type"] == "Day-Ahead"
        mock_service.assert_called_once()

    @patch("routes.v1.lookups.service.get_all_market_types")
    def test_get_market_types_service_error(
        self, mock_service, client, mock_current_user
    ):
        """Test market types retrieval with service error."""
        mock_service.side_effect = Exception("Database error")

        response = client.get("/v1/lookups/market_types")

        assert response.status_code == 500
        response_data = response.json()
        assert response_data["detail"] == "Error retrieving market types"


@pytest.mark.unit
@pytest.mark.controller
class TestLookupsControllerRenewableEnergyTypes:
    """Test renewable energy types lookup endpoint."""

    @patch("routes.v1.lookups.service.get_all_renewable_energy_types")
    def test_get_renewable_energy_types_success(
        self, mock_service, client, mock_current_user
    ):
        """Test successful renewable energy types retrieval."""
        mock_re_types = create_mock_objects(
            ["Solar", "Wind", "Hydro"], "renewable_energy_type"
        )
        mock_service.return_value = mock_re_types

        response = client.get("/v1/lookups/renewable_energy_types")

        assert response.status_code == 200
        response_data = response.json()
        assert "renewable_energy_types" in response_data
        assert len(response_data["renewable_energy_types"]) == 3
        assert (
            response_data["renewable_energy_types"][0]["renewable_energy_type"]
            == "Solar"
        )
        mock_service.assert_called_once()

    @patch("routes.v1.lookups.service.get_all_renewable_energy_types")
    def test_get_renewable_energy_types_service_error(
        self, mock_service, client, mock_current_user
    ):
        """Test renewable energy types retrieval with service error."""
        mock_service.side_effect = Exception("Database error")

        response = client.get("/v1/lookups/renewable_energy_types")

        assert response.status_code == 500
        response_data = response.json()
        assert response_data["detail"] == "Error retrieving renewable energy types"


@pytest.mark.unit
@pytest.mark.controller
class TestLookupsControllerSettlementTypes:
    """Test settlement types lookup endpoint."""

    @patch("routes.v1.lookups.service.get_all_settlement_types")
    def test_get_settlement_types_success(
        self, mock_service, client, mock_current_user
    ):
        """Test successful settlement types retrieval."""
        mock_settlement_types = create_mock_objects(
            ["Financial", "Physical", "Virtual"], "settlement_type"
        )
        mock_service.return_value = mock_settlement_types

        response = client.get("/v1/lookups/settlement_types")

        assert response.status_code == 200
        response_data = response.json()
        assert "settlement_types" in response_data
        assert len(response_data["settlement_types"]) == 3
        assert response_data["settlement_types"][0]["settlement_type"] == "Financial"
        mock_service.assert_called_once()

    @patch("routes.v1.lookups.service.get_all_settlement_types")
    def test_get_settlement_types_service_error(
        self, mock_service, client, mock_current_user
    ):
        """Test settlement types retrieval with service error."""
        mock_service.side_effect = Exception("Database error")

        response = client.get("/v1/lookups/settlement_types")

        assert response.status_code == 500
        response_data = response.json()
        assert response_data["detail"] == "Error retrieving settlement types"


@pytest.mark.unit
@pytest.mark.controller
class TestLookupsControllerConditions:
    """Test conditions lookup endpoint."""

    @patch("routes.v1.lookups.service.get_all_conditions")
    def test_get_conditions_success(self, mock_service, client, mock_current_user):
        """Test successful conditions retrieval."""
        mock_conditions = create_mock_objects(
            ["Greater Than", "Less Than", "Equal To"], "condition"
        )
        mock_service.return_value = mock_conditions

        response = client.get("/v1/lookups/conditions")

        assert response.status_code == 200
        response_data = response.json()
        assert "conditions" in response_data
        assert len(response_data["conditions"]) == 3
        assert response_data["conditions"][0]["condition"] == "Greater Than"
        mock_service.assert_called_once()

    @patch("routes.v1.lookups.service.get_all_conditions")
    def test_get_conditions_service_error(
        self, mock_service, client, mock_current_user
    ):
        """Test conditions retrieval with service error."""
        mock_service.side_effect = Exception("Database error")

        response = client.get("/v1/lookups/conditions")

        assert response.status_code == 500
        response_data = response.json()
        assert response_data["detail"] == "Error retrieving conditions"


@pytest.mark.unit
@pytest.mark.controller
class TestLookupsControllerRatioValues:
    """Test ratio values lookup endpoint."""

    @patch("routes.v1.lookups.service.get_all_ratio_values")
    def test_get_ratio_values_success(self, mock_service, client, mock_current_user):
        """Test successful ratio values retrieval."""
        mock_ratio_values = create_mock_objects(["1:1", "2:1", "1:2"], "ratio_value")
        mock_service.return_value = mock_ratio_values

        response = client.get("/v1/lookups/ratio_values")

        assert response.status_code == 200
        response_data = response.json()
        assert "ratio_values" in response_data
        assert len(response_data["ratio_values"]) == 3
        assert response_data["ratio_values"][0]["ratio_value"] == "1:1"
        mock_service.assert_called_once()

    @patch("routes.v1.lookups.service.get_all_ratio_values")
    def test_get_ratio_values_service_error(
        self, mock_service, client, mock_current_user
    ):
        """Test ratio values retrieval with service error."""
        mock_service.side_effect = Exception("Database error")

        response = client.get("/v1/lookups/ratio_values")

        assert response.status_code == 500
        response_data = response.json()
        assert response_data["detail"] == "Error retrieving ratio values"


@pytest.mark.unit
@pytest.mark.controller
class TestLookupsControllerFactors:
    """Test factors lookup endpoint."""

    @patch("routes.v1.lookups.service.get_all_factors")
    def test_get_factors_success(self, mock_service, client, mock_current_user):
        """Test successful factors retrieval."""
        mock_factors = create_mock_objects(
            ["Load Factor", "Capacity Factor", "Utilization Factor"], "factor"
        )
        mock_service.return_value = mock_factors

        response = client.get("/v1/lookups/factors")

        assert response.status_code == 200
        response_data = response.json()
        assert "factors" in response_data
        assert len(response_data["factors"]) == 3
        assert response_data["factors"][0]["factor"] == "Load Factor"
        mock_service.assert_called_once()

    @patch("routes.v1.lookups.service.get_all_factors")
    def test_get_factors_service_error(self, mock_service, client, mock_current_user):
        """Test factors retrieval with service error."""
        mock_service.side_effect = Exception("Database error")

        response = client.get("/v1/lookups/factors")

        assert response.status_code == 500
        response_data = response.json()
        assert response_data["detail"] == "Error retrieving factors"


@pytest.mark.unit
@pytest.mark.controller
class TestLookupsControllerCarryForwards:
    """Test carry forwards lookup endpoint."""

    @patch("routes.v1.lookups.service.get_all_carry_forwards")
    def test_get_carry_forwards_success(self, mock_service, client, mock_current_user):
        """Test successful carry forwards retrieval."""
        mock_carry_forwards = create_mock_objects(
            ["Yes", "No", "Partial"], "carry_forward"
        )
        mock_service.return_value = mock_carry_forwards

        response = client.get("/v1/lookups/carry_forwards")

        assert response.status_code == 200
        response_data = response.json()
        assert "carry_forwards" in response_data
        assert len(response_data["carry_forwards"]) == 3
        assert response_data["carry_forwards"][0]["carry_forward"] == "Yes"
        mock_service.assert_called_once()

    @patch("routes.v1.lookups.service.get_all_carry_forwards")
    def test_get_carry_forwards_service_error(
        self, mock_service, client, mock_current_user
    ):
        """Test carry forwards retrieval with service error."""
        mock_service.side_effect = Exception("Database error")

        response = client.get("/v1/lookups/carry_forwards")

        assert response.status_code == 500
        response_data = response.json()
        assert response_data["detail"] == "Error retrieving carry forwards"


@pytest.mark.unit
@pytest.mark.controller
class TestLookupsControllerAuthentication:
    """Test authentication requirements for lookups endpoints."""

    def test_lookups_missing_auth(self):
        """Test that all lookup endpoints require authentication."""
        from fastapi.testclient import TestClient

        from app.main import app

        endpoints = [
            "/v1/lookups/states",
            "/v1/lookups/market_types",
            "/v1/lookups/renewable_energy_types",
            "/v1/lookups/settlement_types",
            "/v1/lookups/conditions",
            "/v1/lookups/ratio_values",
            "/v1/lookups/factors",
            "/v1/lookups/carry_forwards",
        ]

        # Create client without authentication overrides
        with TestClient(app) as test_client:
            for endpoint in endpoints:
                response = test_client.get(endpoint)
                # Should fail due to missing authentication
                assert response.status_code in [401, 403], (
                    f"Endpoint {endpoint} should require authentication"
                )


@pytest.mark.unit
@pytest.mark.controller
class TestLookupsControllerErrorHandling:
    """Test error handling scenarios for lookups endpoints."""

    @patch("routes.v1.lookups.service.get_all_states")
    def test_generic_exception_handling(self, mock_service, client, mock_current_user):
        """Test that generic exceptions are properly handled."""
        mock_service.side_effect = RuntimeError("Unexpected error")

        response = client.get("/v1/lookups/states")

        assert response.status_code == 500
        response_data = response.json()
        assert response_data["detail"] == "Error retrieving states"
