"""
Test cases for logic controller functionality.
"""

import uuid
from datetime import datetime, timezone
from decimal import Decimal
from unittest.mock import Mock, patch

import pytest
from entities.logics import Logic
from fastapi import HTTPException, status
from routes.v1.logics import models


@pytest.mark.unit
@pytest.mark.controller
class TestLogicCreateController:
    """Test logic create endpoint."""

    @patch("routes.v1.logics.service.create_logic")
    def test_create_logic_success(self, mock_service, client, mock_current_user):
        """Test successful logic creation."""
        logic_id = uuid.uuid4()
        mock_logic = Logic(
            id=logic_id,
            is_draft=True,
            is_deleted=False,
            created_by=mock_current_user.user_id,
            updated_by=mock_current_user.user_id,
            created_at=datetime.now(timezone.utc),
        )
        mock_logic.logic_id = "LOGIC-********"

        mock_response = models.LogicCreateResponse(
            message="Logic created successfully",
            logic=models.LogicResponse.model_validate(mock_logic)
        )
        mock_service.return_value = mock_response

        logic_data = {
            "customer_ids": [1, 2],
            "demand": {
                "noc_quantum": "100.5000",
                "demand_to_be_bid": "95.0000"
            }
        }

        response = client.post("/v1/logics/", json=logic_data)

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["message"] == "Logic created successfully"
        assert response_data["logic"]["logic_id"] == "LOGIC-********"
        assert response_data["logic"]["is_draft"] is True
        mock_service.assert_called_once()

    @patch("routes.v1.logics.service.create_logic")
    def test_create_logic_minimal_data(self, mock_service, client, mock_current_user):
        """Test logic creation with minimal data."""
        logic_id = uuid.uuid4()
        mock_logic = Logic(
            id=logic_id,
            is_draft=True,
            is_deleted=False,
            created_by=mock_current_user.user_id,
            updated_by=mock_current_user.user_id,
            created_at=datetime.now(timezone.utc),
        )
        mock_logic.logic_id = "LOGIC-********"

        mock_response = models.LogicCreateResponse(
            message="Logic created successfully",
            logic=models.LogicResponse.model_validate(mock_logic)
        )
        mock_service.return_value = mock_response

        logic_data = {}  # Empty data should still work

        response = client.post("/v1/logics/", json=logic_data)

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["message"] == "Logic created successfully"
        mock_service.assert_called_once()

    @patch("routes.v1.logics.service.create_logic")
    def test_create_logic_without_customers(self, mock_service, client, mock_current_user):
        """Test logic creation without customers (customers are optional)."""
        logic_id = uuid.uuid4()
        mock_logic = Logic(
            id=logic_id,
            is_draft=True,
            is_deleted=False,
            created_by=mock_current_user.user_id,
            updated_by=mock_current_user.user_id,
            created_at=datetime.now(timezone.utc),
        )
        mock_logic.logic_id = "LOGIC-********"

        mock_response = models.LogicCreateResponse(
            message="Logic created successfully",
            logic=models.LogicResponse.model_validate(mock_logic)
        )
        mock_service.return_value = mock_response

        # Create logic without customers - should work fine
        logic_data = {
            "demand": {
                "noc_quantum": "100.5000",
                "demand_to_be_bid": "95.0000"
            }
        }

        response = client.post("/v1/logics/", json=logic_data)

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["message"] == "Logic created successfully"
        assert response_data["logic"]["logic_id"] == "LOGIC-********"
        mock_service.assert_called_once()

    @patch("routes.v1.logics.service.create_logic")
    def test_create_logic_with_all_relationships(self, mock_service, client, mock_current_user):
        """Test logic creation with all relationship data."""
        logic_id = uuid.uuid4()
        mock_logic = Logic(
            id=logic_id,
            is_draft=True,
            is_deleted=False,
            created_by=mock_current_user.user_id,
            updated_by=mock_current_user.user_id,
            created_at=datetime.now(timezone.utc),
        )
        mock_logic.logic_id = "LOGIC-********"

        mock_response = models.LogicCreateResponse(
            message="Logic created successfully",
            logic=models.LogicResponse.model_validate(mock_logic)
        )
        mock_service.return_value = mock_response

        logic_data = {
            "customer_ids": [1, 2, 3],
            "demand": {
                "noc_quantum": "100.5000",
                "demand_to_be_bid": "95.0000",
                "demand_safety_factor": "1.1000"
            },
            "market_related_data": [
                {
                    "market_type_id": 1,
                    "percentage_of_demand": "50.0000",
                    "carry_forward_id": 1,
                    "safety_factor": "1.05"
                }
            ],
            "solar_and_other_re": [
                {
                    "re_type_id": 1,
                    "capacity": "100.0000",
                    "cuf": "0.2500",
                    "banking_allowed": True
                }
            ],
            "interstate_transmission_charges": [
                {
                    "charges": "5.0000",
                    "start_date": "2024-01-01"
                }
            ],
            "conditions": [
                {
                    "ratio_value_id": 1,
                    "condition_id": 1,
                    "factor_id": 1,
                    "goal_parameter": "Test Parameter",
                    "operating_value": "10.0000"
                }
            ]
        }

        response = client.post("/v1/logics/", json=logic_data)

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["message"] == "Logic created successfully"
        assert response_data["logic"]["logic_id"] == "LOGIC-********"
        mock_service.assert_called_once()

    @patch("routes.v1.logics.service.create_logic")
    def test_create_logic_service_error(self, mock_service, client, mock_current_user):
        """Test logic creation when service raises HTTPException."""
        mock_service.side_effect = HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customers with IDs [999] not found"
        )

        logic_data = {
            "customer_ids": [999]  # Non-existent customer
        }

        response = client.post("/v1/logics/", json=logic_data)

        assert response.status_code == 404
        response_data = response.json()
        assert "Customers with IDs [999] not found" in response_data["detail"]

    @patch("routes.v1.logics.service.create_logic")
    def test_create_logic_unexpected_error(self, mock_service, client, mock_current_user):
        """Test logic creation when service raises unexpected error."""
        mock_service.side_effect = Exception("Database connection failed")

        logic_data = {"customer_ids": [1]}

        response = client.post("/v1/logics/", json=logic_data)

        assert response.status_code == 500
        response_data = response.json()
        assert response_data["detail"] == "Error creating logic"


@pytest.mark.unit
@pytest.mark.controller
class TestLogicUpdateController:
    """Test logic update endpoint."""

    @patch("routes.v1.logics.service.update_logic")
    def test_update_logic_success(self, mock_service, client, mock_current_user):
        """Test successful logic update."""
        logic_id = uuid.uuid4()
        mock_logic = Logic(
            id=logic_id,
            is_draft=False,  # Changed from draft to published
            is_deleted=False,
            created_by=mock_current_user.user_id,
            updated_by=mock_current_user.user_id,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
        )
        mock_logic.logic_id = "LOGIC-********"

        mock_response = models.LogicUpdateResponse(
            message="Logic updated successfully",
            logic=models.LogicResponse.model_validate(mock_logic)
        )
        mock_service.return_value = mock_response

        update_data = {
            "is_draft": False,
            "customer_ids": [1, 2, 3]
        }

        response = client.patch(f"/v1/logics/{logic_id}", json=update_data)

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["message"] == "Logic updated successfully"
        assert response_data["logic"]["logic_id"] == "LOGIC-********"
        assert response_data["logic"]["is_draft"] is False
        mock_service.assert_called_once()

    @patch("routes.v1.logics.service.update_logic")
    def test_update_logic_partial_data(self, mock_service, client, mock_current_user):
        """Test logic update with partial data (autosave scenario)."""
        logic_id = uuid.uuid4()
        mock_logic = Logic(
            id=logic_id,
            is_draft=True,
            is_deleted=False,
            created_by=mock_current_user.user_id,
            updated_by=mock_current_user.user_id,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
        )
        mock_logic.logic_id = "LOGIC-********"

        mock_response = models.LogicUpdateResponse(
            message="Logic updated successfully",
            logic=models.LogicResponse.model_validate(mock_logic)
        )
        mock_service.return_value = mock_response

        # Only update demand data (autosave scenario)
        update_data = {
            "demand": {
                "noc_quantum": "150.0000",
                "demand_to_be_bid": "140.0000"
            }
        }

        response = client.patch(f"/v1/logics/{logic_id}", json=update_data)

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["message"] == "Logic updated successfully"
        mock_service.assert_called_once()

    @patch("routes.v1.logics.service.update_logic")
    def test_update_logic_not_found(self, mock_service, client, mock_current_user):
        """Test logic update when logic doesn't exist."""
        logic_id = uuid.uuid4()
        mock_service.side_effect = HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Logic with id {logic_id} not found"
        )

        update_data = {"is_draft": False}

        response = client.patch(f"/v1/logics/{logic_id}", json=update_data)

        assert response.status_code == 404
        response_data = response.json()
        assert f"Logic with id {logic_id} not found" in response_data["detail"]

    @patch("routes.v1.logics.service.update_logic")
    def test_update_logic_invalid_uuid(self, mock_service, client, mock_current_user):
        """Test logic update with invalid UUID format."""
        invalid_uuid = "not-a-uuid"

        update_data = {"customer_ids": [1]}

        response = client.patch(f"/v1/logics/{invalid_uuid}", json=update_data)

        assert response.status_code == 422  # Validation error

    @patch("routes.v1.logics.service.update_logic")
    def test_update_logic_change_draft_status(self, mock_service, client, mock_current_user):
        """Test changing logic from draft to published."""
        logic_id = uuid.uuid4()
        mock_logic = Logic(
            id=logic_id,
            is_draft=False,
            is_deleted=False,
            created_by=mock_current_user.user_id,
            updated_by=mock_current_user.user_id,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
        )
        mock_logic.logic_id = "LOGIC-********"

        mock_response = models.LogicUpdateResponse(
            message="Logic updated successfully",
            logic=models.LogicResponse.model_validate(mock_logic)
        )
        mock_service.return_value = mock_response

        update_data = {"is_draft": False}

        response = client.patch(f"/v1/logics/{logic_id}", json=update_data)

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["logic"]["is_draft"] is False
        mock_service.assert_called_once()

    @patch("routes.v1.logics.service.update_logic")
    def test_update_logic_publish_validation_failure(self, mock_service, client, mock_current_user):
        """Test logic publish validation failure when required relationships are missing."""
        logic_id = uuid.uuid4()
        mock_service.side_effect = HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot publish logic. Missing required data: demand, market_related_data"
        )

        update_data = {"is_draft": False}

        response = client.patch(f"/v1/logics/{logic_id}", json=update_data)

        assert response.status_code == 400
        response_data = response.json()
        assert "Cannot publish logic" in response_data["detail"]
        assert "Missing required data" in response_data["detail"]

    @patch("routes.v1.logics.service.update_logic")
    def test_update_logic_date_based_replacement(self, mock_service, client, mock_current_user):
        """Test logic update with date-based replacement for charges."""
        logic_id = uuid.uuid4()
        mock_logic = Logic(
            id=logic_id,
            is_draft=True,
            is_deleted=False,
            created_by=mock_current_user.user_id,
            updated_by=mock_current_user.user_id,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
        )
        mock_logic.logic_id = "LOGIC-********"

        mock_response = models.LogicUpdateResponse(
            message="Logic updated successfully",
            logic=models.LogicResponse.model_validate(mock_logic)
        )
        mock_service.return_value = mock_response

        # Update charges with same start_date (should replace) and different start_date (should create new)
        update_data = {
            "interstate_transmission_charges": [
                {
                    "charges": "10.0000",
                    "start_date": "2024-01-01"  # Same date - should replace existing
                },
                {
                    "charges": "15.0000",
                    "start_date": "2024-02-01"  # Different date - should create new
                }
            ]
        }

        response = client.patch(f"/v1/logics/{logic_id}", json=update_data)

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["message"] == "Logic updated successfully"
        mock_service.assert_called_once()


@pytest.mark.unit
@pytest.mark.controller
class TestLogicDeleteController:
    """Test logic delete endpoint."""

    @patch("routes.v1.logics.service.delete_logic")
    def test_delete_logic_success(self, mock_service, client, mock_current_user):
        """Test successful logic deletion."""
        logic_id = uuid.uuid4()
        mock_response = models.DeleteLogicResponse(
            message="Logic deleted successfully", logic_id=logic_id
        )
        mock_service.return_value = mock_response

        response = client.delete(f"/v1/logics/{logic_id}")

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["message"] == "Logic deleted successfully"
        assert response_data["logic_id"] == str(logic_id)
        mock_service.assert_called_once()

    @patch("routes.v1.logics.service.delete_logic")
    def test_delete_logic_not_found(self, mock_service, client, mock_current_user):
        """Test deletion of non-existent logic."""
        logic_id = uuid.uuid4()
        mock_service.side_effect = HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Logic not found"
        )

        response = client.delete(f"/v1/logics/{logic_id}")

        assert response.status_code == 404
        response_data = response.json()
        assert response_data["detail"] == "Logic not found"

    @patch("routes.v1.logics.service.delete_logic")
    def test_delete_logic_invalid_uuid(self, mock_service, client, mock_current_user):
        """Test deletion with invalid UUID format."""
        invalid_uuid = "invalid-uuid"

        response = client.delete(f"/v1/logics/{invalid_uuid}")

        assert response.status_code == 422  # Validation error
        mock_service.assert_not_called()

    @patch("routes.v1.logics.service.delete_logic")
    def test_delete_logic_service_error(self, mock_service, client, mock_current_user):
        """Test logic deletion with service error."""
        logic_id = uuid.uuid4()
        mock_service.side_effect = HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error deleting logic",
        )

        response = client.delete(f"/v1/logics/{logic_id}")

        assert response.status_code == 500
        response_data = response.json()
        assert response_data["detail"] == "Error deleting logic"

    @patch("routes.v1.logics.service.delete_logic")
    def test_delete_logic_unexpected_error(
        self, mock_service, client, mock_current_user
    ):
        """Test logic deletion with unexpected error."""
        logic_id = uuid.uuid4()
        mock_service.side_effect = Exception("Unexpected error")

        response = client.delete(f"/v1/logics/{logic_id}")

        assert response.status_code == 500
        response_data = response.json()
        assert response_data["detail"] == "Error deleting logic"

    def test_delete_logic_missing_auth(self):
        """Test logic deletion without authentication."""
        from fastapi.testclient import TestClient

        from app.main import app

        logic_id = uuid.uuid4()

        # Create client without authentication overrides
        with TestClient(app) as test_client:
            response = test_client.delete(f"/v1/logics/{logic_id}")

        # Should fail due to missing authentication
        assert response.status_code in [401, 403]

    @patch("routes.v1.logics.service.delete_logic")
    def test_delete_logic_with_valid_uuid_formats(
        self, mock_service, client, mock_current_user
    ):
        """Test deletion with different valid UUID formats."""
        logic_id = uuid.uuid4()
        mock_response = models.DeleteLogicResponse(
            message="Logic deleted successfully", logic_id=logic_id
        )
        mock_service.return_value = mock_response

        # Test with standard UUID format
        response = client.delete(f"/v1/logics/{logic_id}")
        assert response.status_code == 200

        # Test with UUID string with hyphens
        uuid_with_hyphens = str(logic_id)
        response = client.delete(f"/v1/logics/{uuid_with_hyphens}")
        assert response.status_code == 200

    @patch("routes.v1.logics.service.delete_logic")
    def test_delete_logic_response_schema(
        self, mock_service, client, mock_current_user
    ):
        """Test that the response matches the expected schema."""
        logic_id = uuid.uuid4()
        mock_response = models.DeleteLogicResponse(
            message="Logic deleted successfully", logic_id=logic_id
        )
        mock_service.return_value = mock_response

        response = client.delete(f"/v1/logics/{logic_id}")

        assert response.status_code == 200
        response_data = response.json()

        # Verify all required fields are present
        assert "message" in response_data
        assert "logic_id" in response_data
        assert isinstance(response_data["message"], str)
        assert isinstance(response_data["logic_id"], str)

        # Verify the logic_id can be parsed back to UUID
        parsed_uuid = uuid.UUID(response_data["logic_id"])
        assert parsed_uuid == logic_id
