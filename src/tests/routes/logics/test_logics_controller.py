"""
Test cases for logic delete controller functionality.
"""

import uuid
from unittest.mock import patch

import pytest
from fastapi import HTTPException, status
from routes.v1.logics import models


@pytest.mark.unit
@pytest.mark.controller
class TestLogicDeleteController:
    """Test logic delete endpoint."""

    @patch("routes.v1.logics.service.delete_logic")
    def test_delete_logic_success(self, mock_service, client, mock_current_user):
        """Test successful logic deletion."""
        logic_id = uuid.uuid4()
        mock_response = models.DeleteLogicResponse(
            message="Logic deleted successfully",
            logic_id=logic_id
        )
        mock_service.return_value = mock_response

        response = client.delete(f"/v1/logics/{logic_id}")

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["message"] == "Logic deleted successfully"
        assert response_data["logic_id"] == str(logic_id)
        mock_service.assert_called_once()

    @patch("routes.v1.logics.service.delete_logic")
    def test_delete_logic_not_found(self, mock_service, client, mock_current_user):
        """Test deletion of non-existent logic."""
        logic_id = uuid.uuid4()
        mock_service.side_effect = HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Logic not found"
        )

        response = client.delete(f"/v1/logics/{logic_id}")

        assert response.status_code == 404
        response_data = response.json()
        assert response_data["detail"] == "Logic not found"

    @patch("routes.v1.logics.service.delete_logic")
    def test_delete_logic_invalid_uuid(self, mock_service, client, mock_current_user):
        """Test deletion with invalid UUID format."""
        invalid_uuid = "invalid-uuid"

        response = client.delete(f"/v1/logics/{invalid_uuid}")

        assert response.status_code == 422  # Validation error
        mock_service.assert_not_called()

    @patch("routes.v1.logics.service.delete_logic")
    def test_delete_logic_service_error(self, mock_service, client, mock_current_user):
        """Test logic deletion with service error."""
        logic_id = uuid.uuid4()
        mock_service.side_effect = HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error deleting logic"
        )

        response = client.delete(f"/v1/logics/{logic_id}")

        assert response.status_code == 500
        response_data = response.json()
        assert response_data["detail"] == "Error deleting logic"

    @patch("routes.v1.logics.service.delete_logic")
    def test_delete_logic_unexpected_error(self, mock_service, client, mock_current_user):
        """Test logic deletion with unexpected error."""
        logic_id = uuid.uuid4()
        mock_service.side_effect = Exception("Unexpected error")

        response = client.delete(f"/v1/logics/{logic_id}")

        assert response.status_code == 500
        response_data = response.json()
        assert response_data["detail"] == "Error deleting logic"

    def test_delete_logic_missing_auth(self):
        """Test logic deletion without authentication."""
        from fastapi.testclient import TestClient
        from app.main import app
        
        logic_id = uuid.uuid4()
        
        # Create client without authentication overrides
        with TestClient(app) as test_client:
            response = test_client.delete(f"/v1/logics/{logic_id}")
        
        # Should fail due to missing authentication
        assert response.status_code in [401, 403]

    @patch("routes.v1.logics.service.delete_logic")
    def test_delete_logic_with_valid_uuid_formats(self, mock_service, client, mock_current_user):
        """Test deletion with different valid UUID formats."""
        logic_id = uuid.uuid4()
        mock_response = models.DeleteLogicResponse(
            message="Logic deleted successfully",
            logic_id=logic_id
        )
        mock_service.return_value = mock_response

        # Test with standard UUID format
        response = client.delete(f"/v1/logics/{logic_id}")
        assert response.status_code == 200

        # Test with UUID string with hyphens
        uuid_with_hyphens = str(logic_id)
        response = client.delete(f"/v1/logics/{uuid_with_hyphens}")
        assert response.status_code == 200

    @patch("routes.v1.logics.service.delete_logic")
    def test_delete_logic_response_schema(self, mock_service, client, mock_current_user):
        """Test that the response matches the expected schema."""
        logic_id = uuid.uuid4()
        mock_response = models.DeleteLogicResponse(
            message="Logic deleted successfully",
            logic_id=logic_id
        )
        mock_service.return_value = mock_response

        response = client.delete(f"/v1/logics/{logic_id}")

        assert response.status_code == 200
        response_data = response.json()
        
        # Verify all required fields are present
        assert "message" in response_data
        assert "logic_id" in response_data
        assert isinstance(response_data["message"], str)
        assert isinstance(response_data["logic_id"], str)
        
        # Verify the logic_id can be parsed back to UUID
        parsed_uuid = uuid.UUID(response_data["logic_id"])
        assert parsed_uuid == logic_id