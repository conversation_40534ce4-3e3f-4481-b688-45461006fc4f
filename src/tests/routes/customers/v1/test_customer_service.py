from datetime import date, datetime, timezone
from unittest.mock import Mock, patch

import pytest
from entities.customers import Customer
from entities.states import State
from fastapi import HTTPException, status
from routes.v1.customers import models, service


@pytest.mark.unit
@pytest.mark.customer
@pytest.mark.service
class TestCustomerServiceGetAll:
    """Test get all customers functionality."""

    @patch(
        "routes.v1.customers.service.customer_db_services.get_customers_with_count_query"
    )
    def test_get_all_customers_success(
        self, mock_get_customers_with_count, mock_db_session
    ):
        """Test successful retrieval of all customers."""
        # Mock database responses with proper Customer objects
        mock_customers = []
        for i in range(5):
            mock_customer = Mock(spec=Customer)
            mock_customer.id = i + 1
            mock_customer.customer_id = f"EUWI-{i + 1:04d}"
            mock_customer.customer_name = f"Customer {i + 1}"
            mock_customer.onboarded_date = date(2024, 1, i + 1)
            mock_customer.is_deleted = False
            mock_customer.states = []
            mock_customers.append(mock_customer)

        mock_get_customers_with_count.return_value = (mock_customers, 5)

        result = service.get_all_customers(mock_db_session, skip=0, limit=10)

        assert isinstance(result, models.PaginatedCustomerResponse)
        assert len(result.customers) == 5
        assert result.total == 5
        assert result.skip == 0
        assert result.limit == 10
        assert result.has_next is False

    @patch(
        "routes.v1.customers.service.customer_db_services.get_customers_with_count_query"
    )
    def test_get_all_customers_with_pagination(
        self, mock_get_customers_with_count, mock_db_session
    ):
        """Test customer retrieval with pagination."""
        mock_customers = []
        for i in range(2):
            mock_customer = Mock(spec=Customer)
            mock_customer.id = i + 3  # Skip first 2 for pagination
            mock_customer.customer_id = f"EUWI-{i + 3:04d}"
            mock_customer.customer_name = f"Customer {i + 3}"
            mock_customer.onboarded_date = date(2024, 1, i + 3)
            mock_customer.is_deleted = False
            mock_customer.states = []
            mock_customers.append(mock_customer)

        mock_get_customers_with_count.return_value = (mock_customers, 5)

        result = service.get_all_customers(mock_db_session, skip=2, limit=2)

        assert isinstance(result, models.PaginatedCustomerResponse)
        assert len(result.customers) == 2
        assert result.total == 5
        assert result.skip == 2
        assert result.limit == 2
        assert result.has_next is True

    @patch(
        "routes.v1.customers.service.customer_db_services.get_customers_with_count_query"
    )
    def test_get_all_customers_with_search(
        self, mock_get_customers_with_count, mock_db_session
    ):
        """Test customer retrieval with search."""
        mock_customer = Mock(spec=Customer)
        mock_customer.id = 1
        mock_customer.customer_id = "EUWI-0001"
        mock_customer.customer_name = "Customer 1"
        mock_customer.onboarded_date = date(2024, 1, 1)
        mock_customer.is_deleted = False
        mock_customer.states = []

        mock_get_customers_with_count.return_value = ([mock_customer], 1)

        result = service.get_all_customers(
            mock_db_session, skip=0, limit=10, search="Customer 1"
        )

        assert isinstance(result, models.PaginatedCustomerResponse)
        assert len(result.customers) == 1
        assert result.customers[0].customer_name == "Customer 1"

    @patch(
        "routes.v1.customers.service.customer_db_services.get_customers_with_count_query"
    )
    def test_get_all_customers_empty_search(
        self, mock_get_customers_with_count, mock_db_session
    ):
        """Test customer retrieval with empty search string."""
        mock_customers = []
        for i in range(5):
            mock_customer = Mock(spec=Customer)
            mock_customer.id = i + 1
            mock_customer.customer_id = f"EUWI-{i + 1:04d}"
            mock_customer.customer_name = f"Customer {i + 1}"
            mock_customer.onboarded_date = date(2024, 1, i + 1)
            mock_customer.is_deleted = False
            mock_customer.states = []
            mock_customers.append(mock_customer)

        mock_get_customers_with_count.return_value = (mock_customers, 5)

        result = service.get_all_customers(
            mock_db_session, skip=0, limit=10, search="   "
        )

        assert isinstance(result, models.PaginatedCustomerResponse)
        assert len(result.customers) == 5  # Should return all customers

    @patch(
        "routes.v1.customers.service.customer_db_services.get_customers_with_count_query"
    )
    def test_get_all_customers_database_error(
        self, mock_get_customers_with_count, mock_db_session
    ):
        """Test customer retrieval with database error."""
        mock_get_customers_with_count.side_effect = Exception("Database error")

        with pytest.raises(HTTPException) as exc_info:
            service.get_all_customers(mock_db_session)

        assert exc_info.value.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR

    @patch(
        "routes.v1.customers.service.customer_db_services.get_customers_with_count_query"
    )
    def test_get_all_customers_empty_result(
        self, mock_get_customers_with_count, mock_db_session
    ):
        """Test customer retrieval with no customers found."""
        mock_get_customers_with_count.return_value = ([], 0)

        result = service.get_all_customers(mock_db_session, skip=0, limit=10)

        assert isinstance(result, models.PaginatedCustomerResponse)
        assert len(result.customers) == 0
        assert result.total == 0
        assert result.skip == 0
        assert result.limit == 10
        assert result.has_next is False


@pytest.mark.unit
@pytest.mark.customer
@pytest.mark.service
class TestCustomerServiceValidation:
    """Test customer service validation functions."""

    @patch("routes.v1.customers.service.states_db_services.get_states_by_ids")
    def test_validate_and_get_states_success(self, mock_get_states, mock_db_session):
        """Test successful state validation."""
        # Mock states
        mock_state1 = Mock()
        mock_state1.id = 1
        mock_state2 = Mock()
        mock_state2.id = 2
        mock_get_states.return_value = [mock_state1, mock_state2]

        state_ids = [1, 2]
        result = service.validate_and_get_states(mock_db_session, state_ids)

        assert len(result) == 2
        assert {state.id for state in result} == {1, 2}

    def test_validate_and_get_states_empty_list(self, mock_db_session):
        """Test state validation with empty list."""
        result = service.validate_and_get_states(mock_db_session, [])

        assert result == []

    @patch("routes.v1.customers.service.states_db_services.get_states_by_ids")
    def test_validate_and_get_states_missing_states(
        self, mock_get_states, mock_db_session
    ):
        """Test state validation with missing states."""
        # Mock only one state found (state 1), but not state 999
        mock_state1 = Mock()
        mock_state1.id = 1
        mock_get_states.return_value = [mock_state1]

        state_ids = [1, 999]  # 999 doesn't exist

        with pytest.raises(HTTPException) as exc_info:
            service.validate_and_get_states(mock_db_session, state_ids)

        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "States not found" in exc_info.value.detail
        assert "999" in exc_info.value.detail


@pytest.mark.unit
@pytest.mark.customer
@pytest.mark.service
class TestCustomerServiceCreate:
    """Test customer creation functionality."""

    @patch("routes.v1.customers.service.customer_db_services.create_customer_query")
    @patch("routes.v1.customers.service.validate_and_get_states")
    def test_create_customer_success(
        self, mock_validate_states, mock_create, mock_db_session, mock_current_user
    ):
        """Test successful customer creation."""
        # Mock validated states - create real State objects
        mock_state1 = State(id=1, name="California")
        mock_state2 = State(id=2, name="Texas")
        mock_states = [mock_state1, mock_state2]
        mock_validate_states.return_value = mock_states

        # Mock created customer - create real Customer object
        mock_customer = Customer(
            id=1,
            customer_name="New Customer",
            onboarded_date=date(2024, 2, 1),
            created_by=mock_current_user.user_id,
            updated_by=mock_current_user.user_id,
            states=mock_states,
        )
        mock_customer.customer_id = "EUWI-0001"
        mock_customer.is_deleted = False
        mock_customer.created_at = datetime.now(timezone.utc)
        mock_customer.updated_at = datetime.now(timezone.utc)
        mock_create.return_value = mock_customer

        customer_data = models.CustomerCreate(
            customer_name="New Customer",
            onboarded_date=date(2024, 2, 1),
            state_ids=[1, 2],
        )

        result = service.create_customer(
            mock_db_session, mock_current_user, customer_data
        )

        assert result.customer_name == customer_data.customer_name
        assert result.onboarded_date == customer_data.onboarded_date
        assert result.created_by == mock_current_user.user_id
        assert result.updated_by == mock_current_user.user_id
        assert len(result.states) == 2

    @patch("routes.v1.customers.service.customer_db_services.create_customer_query")
    def test_create_customer_without_states(
        self, mock_create, mock_db_session, mock_current_user
    ):
        """Test customer creation without states."""
        mock_customer = Mock(spec=Customer)
        mock_customer.customer_name = "Customer Without States"
        mock_customer.states = []
        mock_create.return_value = mock_customer

        customer_data = models.CustomerCreate(
            customer_name="Customer Without States",
            onboarded_date=date(2024, 2, 1),
            state_ids=[],
        )

        result = service.create_customer(
            mock_db_session, mock_current_user, customer_data
        )

        assert result.customer_name == customer_data.customer_name
        assert len(result.states) == 0

    @patch("routes.v1.customers.service.validate_and_get_states")
    def test_create_customer_invalid_states(
        self, mock_validate_states, mock_db_session, mock_current_user
    ):
        """Test customer creation with invalid states."""
        mock_validate_states.side_effect = HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="States not found: 999"
        )

        customer_data = models.CustomerCreate(
            customer_name="Customer With Invalid States",
            onboarded_date=date(2024, 2, 1),
            state_ids=[999],  # Non-existent state
        )

        with pytest.raises(HTTPException) as exc_info:
            service.create_customer(mock_db_session, mock_current_user, customer_data)

        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST

    @patch("routes.v1.customers.service.customer_db_services.create_customer_query")
    def test_create_customer_database_error(
        self, mock_create, mock_db_session, mock_current_user
    ):
        """Test customer creation with database error."""
        mock_create.side_effect = Exception("Database error")

        customer_data = models.CustomerCreate(
            customer_name="Test Customer", onboarded_date=date(2024, 2, 1), state_ids=[]
        )

        with pytest.raises(HTTPException) as exc_info:
            service.create_customer(mock_db_session, mock_current_user, customer_data)

        assert exc_info.value.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR


@pytest.mark.unit
@pytest.mark.customer
@pytest.mark.service
class TestCustomerServiceGetById:
    """Test get customer by ID functionality."""

    @patch("routes.v1.customers.service.customer_db_services.get_customer_by_id_query")
    def test_get_customer_by_id_success(self, mock_get_customer, mock_db_session):
        """Test successful customer retrieval by ID."""
        mock_customer = Mock(spec=Customer)
        mock_customer.id = 1
        mock_customer.customer_name = "Test Customer"
        mock_get_customer.return_value = mock_customer

        result = service.get_customer_by_id(mock_db_session, 1)

        assert result is not None
        assert result.id == 1
        assert result.customer_name == "Test Customer"

    @patch("routes.v1.customers.service.customer_db_services.get_customer_by_id_query")
    def test_get_customer_by_id_not_found(self, mock_get_customer, mock_db_session):
        """Test customer retrieval with non-existent ID."""
        mock_get_customer.return_value = None

        result = service.get_customer_by_id(mock_db_session, 999)

        assert result is None


@pytest.mark.unit
@pytest.mark.customer
@pytest.mark.service
class TestCustomerServiceUpdate:
    """Test customer update functionality."""

    @patch("routes.v1.customers.service.customer_db_services.update_customer_query")
    @patch("routes.v1.customers.service.customer_db_services.get_customer_by_id_query")
    @patch("routes.v1.customers.service.validate_and_get_states")
    def test_update_customer_success(
        self,
        mock_validate_states,
        mock_get_customer,
        mock_update,
        mock_db_session,
        mock_current_user,
    ):
        """Test successful customer update."""
        # Mock existing customer
        mock_customer = Mock(spec=Customer)
        mock_customer.id = 1
        mock_customer.customer_name = "Original Name"
        mock_customer.states = []
        mock_get_customer.return_value = mock_customer

        # Mock validated states
        mock_state = Mock()
        mock_validate_states.return_value = [mock_state]

        # Mock updated customer
        mock_customer.customer_name = "Updated Customer Name"
        mock_customer.updated_by = mock_current_user.user_id
        mock_customer.states = [mock_state]
        mock_update.return_value = mock_customer

        update_data = models.CustomerUpdateRequest(
            customer_name="Updated Customer Name",
            state_ids=[1],  # Change states
        )

        result = service.update_customer(
            mock_db_session, mock_current_user, 1, update_data
        )

        assert result.customer_name == "Updated Customer Name"
        assert result.updated_by == mock_current_user.user_id
        assert len(result.states) == 1

    @patch("routes.v1.customers.service.customer_db_services.update_customer_query")
    @patch("routes.v1.customers.service.customer_db_services.get_customer_by_id_query")
    def test_update_customer_partial_update(
        self, mock_get_customer, mock_update, mock_db_session, mock_current_user
    ):
        """Test partial customer update."""
        mock_customer = Mock(spec=Customer)
        mock_customer.id = 1
        mock_customer.customer_name = "Original Name"
        mock_get_customer.return_value = mock_customer

        mock_customer.customer_name = "Partially Updated Name"
        mock_customer.updated_by = mock_current_user.user_id
        mock_update.return_value = mock_customer

        update_data = models.CustomerUpdateRequest(
            customer_name="Partially Updated Name"
            # Only updating name, not states
        )

        result = service.update_customer(
            mock_db_session, mock_current_user, 1, update_data
        )

        assert result.customer_name == "Partially Updated Name"
        assert result.updated_by == mock_current_user.user_id

    @patch("routes.v1.customers.service.customer_db_services.get_customer_by_id_query")
    def test_update_customer_not_found(
        self, mock_get_customer, mock_db_session, mock_current_user
    ):
        """Test update of non-existent customer."""
        mock_get_customer.return_value = None

        update_data = models.CustomerUpdateRequest(customer_name="Updated Name")

        with pytest.raises(HTTPException) as exc_info:
            service.update_customer(
                mock_db_session, mock_current_user, 999, update_data
            )

        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert "not found" in exc_info.value.detail

    @patch("routes.v1.customers.service.customer_db_services.get_customer_by_id_query")
    @patch("routes.v1.customers.service.validate_and_get_states")
    def test_update_customer_invalid_states(
        self,
        mock_validate_states,
        mock_get_customer,
        mock_db_session,
        mock_current_user,
    ):
        """Test customer update with invalid states."""
        mock_customer = Mock(spec=Customer)
        mock_get_customer.return_value = mock_customer

        mock_validate_states.side_effect = HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="States not found: 999"
        )

        update_data = models.CustomerUpdateRequest(
            state_ids=[999]  # Non-existent state
        )

        with pytest.raises(HTTPException) as exc_info:
            service.update_customer(mock_db_session, mock_current_user, 1, update_data)

        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST

    @patch("routes.v1.customers.service.customer_db_services.update_customer_query")
    @patch("routes.v1.customers.service.customer_db_services.get_customer_by_id_query")
    def test_update_customer_database_error(
        self, mock_get_customer, mock_update, mock_db_session, mock_current_user
    ):
        """Test customer update with database error."""
        mock_customer = Mock(spec=Customer)
        mock_get_customer.return_value = mock_customer
        mock_update.side_effect = Exception("Database error")

        update_data = models.CustomerUpdateRequest(customer_name="Updated Name")

        with pytest.raises(HTTPException) as exc_info:
            service.update_customer(mock_db_session, mock_current_user, 1, update_data)

        assert exc_info.value.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR


@pytest.mark.unit
@pytest.mark.customer
@pytest.mark.service
class TestCustomerServiceDelete:
    """Test customer deletion functionality."""

    @patch("routes.v1.customers.service.customer_db_services.delete_customer_query")
    def test_delete_customer_success(self, mock_delete, mock_db_session):
        """Test successful customer deletion."""
        mock_customer = Mock(spec=Customer)
        mock_customer.is_deleted = False

        # Should not raise any exception
        service.delete_customer(mock_db_session, mock_customer)

        # Verify delete query was called
        mock_delete.assert_called_once_with(mock_db_session, mock_customer)

    @patch("routes.v1.customers.service.customer_db_services.delete_customer_query")
    def test_delete_customer_database_error(self, mock_delete, mock_db_session):
        """Test customer deletion with database error."""
        mock_delete.side_effect = Exception("Database error")
        mock_customer = Mock(spec=Customer)

        with pytest.raises(HTTPException) as exc_info:
            service.delete_customer(mock_db_session, mock_customer)

        assert exc_info.value.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
