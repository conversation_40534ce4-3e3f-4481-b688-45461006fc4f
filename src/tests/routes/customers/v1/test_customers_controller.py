from datetime import date
from unittest.mock import patch

import pytest
from routes.v1.customers import models


@pytest.mark.unit
@pytest.mark.customer
@pytest.mark.controller
class TestCustomerControllerList:
    """Test customer list endpoint."""

    @patch("routes.v1.customers.service.get_all_customers")
    def test_list_customers_success(self, mock_service, client):
        """Test successful customer listing."""
        # Mock service response
        mock_response = models.PaginatedCustomerResponse(
            customers=[
                models.CustomerGetResponse(
                    id=1,
                    customer_id="EUWI-0001",
                    customer_name="Test Customer",
                    onboarded_date=date(2024, 1, 1),
                    states=[],
                )
            ],
            total=1,
            skip=0,
            limit=100,
            has_next=False,
        )
        mock_service.return_value = mock_response

        response = client.get("/v1/customers/")

        assert response.status_code == 200
        response_data = response.json()
        assert len(response_data["customers"]) == 1
        assert response_data["total"] == 1
        assert response_data["skip"] == 0
        assert response_data["limit"] == 100

    @patch("routes.v1.customers.service.get_all_customers")
    @patch("routes.v1.auth.service.get_current_user")
    def test_list_customers_with_pagination(
        self, mock_auth, mock_service, client, mock_current_user
    ):
        """Test customer listing with pagination parameters."""
        mock_auth.return_value = mock_current_user
        mock_service.return_value = models.PaginatedCustomerResponse(
            customers=[], total=0, skip=10, limit=5, has_next=False
        )

        response = client.get(
            "/v1/customers/?skip=10&limit=5",
            headers={"Authorization": "Bearer mock-token"},
        )

        assert response.status_code == 200
        mock_service.assert_called_once()
        call_kwargs = mock_service.call_args.kwargs
        assert call_kwargs["skip"] == 10
        assert call_kwargs["limit"] == 5

    @patch("routes.v1.customers.service.get_all_customers")
    @patch("routes.v1.auth.service.get_current_user")
    def test_list_customers_with_search(
        self, mock_auth, mock_service, client, mock_current_user
    ):
        """Test customer listing with search parameter."""
        mock_auth.return_value = mock_current_user
        mock_service.return_value = models.PaginatedCustomerResponse(
            customers=[], total=0, skip=0, limit=100, has_next=False
        )

        response = client.get(
            "/v1/customers/?search=test", headers={"Authorization": "Bearer mock-token"}
        )

        assert response.status_code == 200
        mock_service.assert_called_once()
        call_kwargs = mock_service.call_args.kwargs
        assert call_kwargs["search"] == "test"

    @patch("routes.v1.customers.service.get_all_customers")
    @patch("routes.v1.auth.service.get_current_user")
    def test_list_customers_service_error(
        self, mock_auth, mock_service, client, mock_current_user
    ):
        """Test customer listing with service error."""
        mock_auth.return_value = mock_current_user
        mock_service.side_effect = Exception("Service error")

        response = client.get(
            "/v1/customers/", headers={"Authorization": "Bearer mock-token"}
        )

        assert response.status_code == 500

    def test_list_customers_invalid_pagination(self, client):
        """Test customer listing with invalid pagination parameters."""
        response = client.get(
            "/v1/customers/?skip=-1&limit=0",
            headers={"Authorization": "Bearer mock-token"},
        )

        assert response.status_code == 422  # Validation error


@pytest.mark.unit
@pytest.mark.customer
@pytest.mark.controller
class TestCustomerControllerCreate:
    """Test customer creation endpoint."""

    @patch("routes.v1.customers.service.create_customer")
    def test_create_customer_success(self, mock_service, client):
        """Test successful customer creation."""
        # Create a proper Customer response model
        from entities.customers import Customer
        from entities.states import State

        mock_customer = Customer(
            id=1,
            customer_name="New Customer",
            onboarded_date=date(2024, 2, 1),
            created_by="test-user-id",
            updated_by="test-user-id",
            states=[],
        )
        mock_customer.customer_id = "EUWI-0001"
        mock_customer.is_deleted = False
        mock_service.return_value = mock_customer

        customer_data = {
            "customer_name": "New Customer",
            "onboarded_date": "2024-02-01",
            "state_ids": [1, 2],
        }

        response = client.post("/v1/customers/", json=customer_data)

        assert response.status_code == 201
        mock_service.assert_called_once()

    @patch("routes.v1.customers.service.create_customer")
    @patch("routes.v1.auth.service.get_current_user")
    def test_create_customer_invalid_data(
        self, mock_auth, mock_service, client, mock_current_user
    ):
        """Test customer creation with invalid data."""
        mock_auth.return_value = mock_current_user

        # Missing required customer_name
        customer_data = {"onboarded_date": "2024-02-01", "state_ids": [1, 2]}

        response = client.post(
            "/v1/customers/",
            json=customer_data,
            headers={"Authorization": "Bearer mock-token"},
        )

        assert response.status_code == 422  # Validation error

    @patch("routes.v1.customers.service.create_customer")
    @patch("routes.v1.auth.service.get_current_user")
    def test_create_customer_service_error(
        self, mock_auth, mock_service, client, mock_current_user
    ):
        """Test customer creation with service error."""
        mock_auth.return_value = mock_current_user
        mock_service.side_effect = Exception("Service error")

        customer_data = {
            "customer_name": "New Customer",
            "onboarded_date": "2024-02-01",
            "state_ids": [],
        }

        response = client.post(
            "/v1/customers/",
            json=customer_data,
            headers={"Authorization": "Bearer mock-token"},
        )

        assert response.status_code == 500


@pytest.mark.unit
@pytest.mark.customer
@pytest.mark.controller
class TestCustomerControllerGetById:
    """Test get customer by ID endpoint."""

    @patch("routes.v1.customers.service.get_customer_by_id")
    @patch("routes.v1.auth.service.get_current_user")
    def test_get_customer_by_id_success(
        self, mock_auth, mock_service, client, mock_current_user, sample_customer
    ):
        """Test successful customer retrieval by ID."""
        mock_auth.return_value = mock_current_user
        mock_service.return_value = sample_customer

        response = client.get(
            f"/v1/customers/{sample_customer.id}",
            headers={"Authorization": "Bearer mock-token"},
        )

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["id"] == sample_customer.id
        assert response_data["customer_name"] == sample_customer.customer_name

    @patch("routes.v1.customers.service.get_customer_by_id")
    @patch("routes.v1.auth.service.get_current_user")
    def test_get_customer_by_id_not_found(
        self, mock_auth, mock_service, client, mock_current_user
    ):
        """Test customer retrieval with non-existent ID."""
        mock_auth.return_value = mock_current_user
        mock_service.return_value = None

        response = client.get(
            "/v1/customers/999", headers={"Authorization": "Bearer mock-token"}
        )

        assert response.status_code == 404

    @patch("routes.v1.customers.service.get_customer_by_id")
    @patch("routes.v1.auth.service.get_current_user")
    def test_get_customer_by_id_service_error(
        self, mock_auth, mock_service, client, mock_current_user
    ):
        """Test customer retrieval with service error."""
        mock_auth.return_value = mock_current_user
        mock_service.side_effect = Exception("Service error")

        response = client.get(
            "/v1/customers/1", headers={"Authorization": "Bearer mock-token"}
        )

        assert response.status_code == 500


@pytest.mark.unit
@pytest.mark.customer
@pytest.mark.controller
class TestCustomerControllerUpdate:
    """Test customer update endpoint."""

    @patch("routes.v1.customers.service.get_customer_by_id")
    @patch("routes.v1.customers.service.update_customer")
    @patch("routes.v1.auth.service.get_current_user")
    def test_update_customer_success(
        self,
        mock_auth,
        mock_update,
        mock_get,
        client,
        mock_current_user,
        sample_customer,
    ):
        """Test successful customer update."""
        mock_auth.return_value = mock_current_user
        mock_get.return_value = sample_customer
        mock_update.return_value = sample_customer

        update_data = {"customer_name": "Updated Customer Name", "state_ids": [1]}

        response = client.patch(
            f"/v1/customers/{sample_customer.id}",
            json=update_data,
            headers={"Authorization": "Bearer mock-token"},
        )

        assert response.status_code == 200
        mock_update.assert_called_once()

    @patch("routes.v1.customers.service.get_customer_by_id")
    @patch("routes.v1.auth.service.get_current_user")
    def test_update_customer_not_found(
        self, mock_auth, mock_get, client, mock_current_user
    ):
        """Test update of non-existent customer."""
        mock_auth.return_value = mock_current_user
        mock_get.return_value = None

        update_data = {"customer_name": "Updated Name"}

        response = client.patch(
            "/v1/customers/999",
            json=update_data,
            headers={"Authorization": "Bearer mock-token"},
        )

        assert response.status_code == 404

    @patch("routes.v1.customers.service.get_customer_by_id")
    @patch("routes.v1.customers.service.update_customer")
    @patch("routes.v1.auth.service.get_current_user")
    def test_update_customer_service_error(
        self,
        mock_auth,
        mock_update,
        mock_get,
        client,
        mock_current_user,
        sample_customer,
    ):
        """Test customer update with service error."""
        mock_auth.return_value = mock_current_user
        mock_get.return_value = sample_customer
        mock_update.side_effect = Exception("Service error")

        update_data = {"customer_name": "Updated Name"}

        response = client.patch(
            f"/v1/customers/{sample_customer.id}",
            json=update_data,
            headers={"Authorization": "Bearer mock-token"},
        )

        assert response.status_code == 500


@pytest.mark.unit
@pytest.mark.customer
@pytest.mark.controller
class TestCustomerControllerDelete:
    """Test customer deletion endpoint."""

    @patch("routes.v1.customers.service.get_customer_by_id")
    @patch("routes.v1.customers.service.delete_customer")
    @patch("routes.v1.auth.service.get_current_user")
    def test_delete_customer_success(
        self,
        mock_auth,
        mock_delete,
        mock_get,
        client,
        mock_current_user,
        sample_customer,
    ):
        """Test successful customer deletion."""
        mock_auth.return_value = mock_current_user
        mock_get.return_value = sample_customer

        response = client.delete(
            f"/v1/customers/{sample_customer.id}",
            headers={"Authorization": "Bearer mock-token"},
        )

        assert response.status_code == 204
        mock_delete.assert_called_once()

    @patch("routes.v1.customers.service.get_customer_by_id")
    @patch("routes.v1.auth.service.get_current_user")
    def test_delete_customer_not_found(
        self, mock_auth, mock_get, client, mock_current_user
    ):
        """Test deletion of non-existent customer."""
        mock_auth.return_value = mock_current_user
        mock_get.return_value = None

        response = client.delete(
            "/v1/customers/999", headers={"Authorization": "Bearer mock-token"}
        )

        assert response.status_code == 404

    @patch("routes.v1.customers.service.get_customer_by_id")
    @patch("routes.v1.customers.service.delete_customer")
    @patch("routes.v1.auth.service.get_current_user")
    def test_delete_customer_service_error(
        self,
        mock_auth,
        mock_delete,
        mock_get,
        client,
        mock_current_user,
        sample_customer,
    ):
        """Test customer deletion with service error."""
        mock_auth.return_value = mock_current_user
        mock_get.return_value = sample_customer
        mock_delete.side_effect = Exception("Service error")

        response = client.delete(
            f"/v1/customers/{sample_customer.id}",
            headers={"Authorization": "Bearer mock-token"},
        )

        assert response.status_code == 500
