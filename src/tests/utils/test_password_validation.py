"""
Comprehensive unit tests for password validation utilities.
"""

import pytest
from utils.password_validation import (
    PasswordValidationError,
    PasswordValidator,
    validate_password_strength,
)


class TestPasswordValidator:
    """Test cases for PasswordValidator class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.validator = PasswordValidator()

    def test_valid_strong_password(self):
        """Test validation of a strong password."""
        strong_password = "MyStr0ng!P@ssw0rd"
        assert self.validator.validate_password(strong_password) is True

    def test_password_too_short(self):
        """Test validation of password that is too short."""
        short_password = "Abc1!"
        with pytest.raises(PasswordValidationError) as exc_info:
            self.validator.validate_password(short_password)
        assert "at least 8 characters long" in str(exc_info.value)

    def test_password_too_long(self):
        """Test validation of password that is too long."""
        long_password = "A" * 129 + "1!"
        with pytest.raises(PasswordValidationError) as exc_info:
            self.validator.validate_password(long_password)
        assert "must not exceed 128 characters" in str(exc_info.value)

    def test_password_missing_uppercase(self):
        """Test validation of password missing uppercase letters."""
        password = "mystrongpassword1!"
        with pytest.raises(PasswordValidationError) as exc_info:
            self.validator.validate_password(password)
        assert "uppercase letter" in str(exc_info.value)

    def test_password_missing_lowercase(self):
        """Test validation of password missing lowercase letters."""
        password = "MYSTRONGPASSWORD1!"
        with pytest.raises(PasswordValidationError) as exc_info:
            self.validator.validate_password(password)
        assert "lowercase letter" in str(exc_info.value)

    def test_password_missing_numbers(self):
        """Test validation of password missing numbers."""
        password = "MyStrongPassword!"
        with pytest.raises(PasswordValidationError) as exc_info:
            self.validator.validate_password(password)
        assert "at least one number" in str(exc_info.value)

    def test_password_missing_special_chars(self):
        """Test validation of password missing special characters."""
        password = "MyStrongPassword1"
        with pytest.raises(PasswordValidationError) as exc_info:
            self.validator.validate_password(password)
        assert "special character" in str(exc_info.value)

    def test_common_password_rejection(self):
        """Test rejection of common passwords."""
        common_passwords = ["password", "123456", "password123", "qwerty"]
        for password in common_passwords:
            with pytest.raises(PasswordValidationError) as exc_info:
                self.validator.validate_password(password)
            assert "too common" in str(exc_info.value)

    def test_sequential_pattern_rejection(self):
        """Test rejection of passwords with sequential patterns."""
        password = "MyPassword123456!"
        with pytest.raises(PasswordValidationError) as exc_info:
            self.validator.validate_password(password)
        assert "sequential characters" in str(exc_info.value)

    def test_repeated_characters(self):
        """Test rejection of passwords with too many repeated characters."""
        password = "MyPassssss1!"
        with pytest.raises(PasswordValidationError) as exc_info:
            self.validator.validate_password(password)
        assert "repeated characters" in str(exc_info.value)

    def test_only_numbers_password(self):
        """Test rejection of passwords that are only numbers."""
        password = "12345678"
        with pytest.raises(PasswordValidationError) as exc_info:
            self.validator.validate_password(password)
        assert "only numbers" in str(exc_info.value)

    def test_only_letters_password(self):
        """Test rejection of passwords that are only letters."""
        password = "MyPassword"
        with pytest.raises(PasswordValidationError) as exc_info:
            self.validator.validate_password(password)
        assert "only letters" in str(exc_info.value)

    def test_password_strength_scoring(self):
        """Test password strength scoring algorithm."""
        # Weak password
        weak_score = self.validator.get_password_strength_score("pass")
        assert weak_score < 30

        # Medium password
        medium_score = self.validator.get_password_strength_score("MyPass123")
        assert 30 <= medium_score < 70

        # Strong password
        strong_score = self.validator.get_password_strength_score("MyStr0ng!P@ssw0rd")
        assert strong_score >= 70

    def test_improvement_suggestions(self):
        """Test password improvement suggestions."""
        weak_password = "weak"
        suggestions = self.validator.suggest_improvements(weak_password)

        assert any("length" in suggestion.lower() for suggestion in suggestions)
        assert any("uppercase" in suggestion.lower() for suggestion in suggestions)
        assert any("numbers" in suggestion.lower() for suggestion in suggestions)
        assert any("special" in suggestion.lower() for suggestion in suggestions)


class TestPasswordValidationFunction:
    """Test cases for the validate_password_strength function."""

    def test_valid_password_returns_password(self):
        """Test that valid password is returned unchanged."""
        valid_password = "MyStr0ng!P@ssw0rd"
        result = validate_password_strength(valid_password)
        assert result == valid_password

    def test_invalid_password_raises_value_error(self):
        """Test that invalid password raises ValueError."""
        invalid_password = "weak"
        with pytest.raises(ValueError) as exc_info:
            validate_password_strength(invalid_password)
        assert "Password validation failed" in str(exc_info.value)

    def test_multiple_validation_errors(self):
        """Test that multiple validation errors are captured."""
        invalid_password = "a"  # Too short, missing requirements
        with pytest.raises(ValueError) as exc_info:
            validate_password_strength(invalid_password)
        error_message = str(exc_info.value)

        # Should contain multiple error messages
        assert "at least 8 characters" in error_message
        assert "uppercase letter" in error_message
        assert "number" in error_message
        assert "special character" in error_message
