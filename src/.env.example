# Database Configuration
DATABASE_URL="postgresql+psycopg://YOUR_DB_USER:YOUR_DB_PASSWORD@kimbal-logic-db:5432/kimbal_logic_db"
POSTGRES_DB=kimbal_logic_db
POSTGRES_USER=YOUR_DB_USER
POSTGRES_PASSWORD=YOUR_DB_PASSWORD  # REQUIRED: Set a strong password (min 16 chars, mixed case, numbers, symbols)
POSTGRES_PORT=5432

# JWT Authentication
# SECURITY: Generate secret key with: openssl rand -base64 64
SECRET_KEY='ENTER_YOUR_SECRET_KEY_HERE' # REQUIRED: Minimum 32 characters
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Application Settings
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# CORS Settings (comma-separated list of allowed origins)
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080

# Rate Limiting
RATE_LIMIT_ENABLED=true

# Password Security Settings
MIN_PASSWORD_LENGTH=8
REQUIRE_UPPERCASE=true
REQUIRE_LOWERCASE=true
REQUIRE_NUMBERS=true
REQUIRE_SPECIAL_CHARS=true
