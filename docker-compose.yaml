services:
  kimbal-logic-backend:
    build:
      context: ./src
      dockerfile: Dockerfile
    container_name: kimbal-logic-backend
    hostname: kimbal-logic-backend
    env_file: "./src/.env"
    ports:
      - "8000:8000"
    volumes:
      - ./logs:/app/logs
    depends_on:
      kimbal-logic-db:
        condition: service_healthy
    healthcheck:
      test: [ "CMD-SHELL", "curl -f http://localhost:8000/health || exit 1" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

  kimbal-logic-celery:
    build:
      context: ./src
    container_name: kimbal-logic-celery
    hostname: kimbal-logic-celery
    command: celery -A utils.celery_worker worker --loglevel=debug
    env_file: "src/.env"
    volumes:
      - ./logs:/app/logs
    depends_on:
      kimbal-logic-redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "celery -A utils.celery_worker inspect ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  kimbal-logic-celery-beat:
    build:
      context: ./src
    container_name: kimbal-logic-celery-beat
    hostname: kimbal-logic-celery-beat
    command: celery -A utils.celery_worker beat --loglevel=info
    env_file: "src/.env"
    volumes:
      - ./logs:/app/logs
      - ./static:/app/static
    depends_on:
      - kimbal-logic-celery
    restart: unless-stopped

  kimbal-logic-redis:
    image: redis:8.2.1-alpine
    container_name: kimbal-logic-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: sh -c "test -f appendonly.aof || touch appendonly.aof ; echo y | redis-check-aof --fix appendonly.aof ; redis-server --save 60 1 --loglevel warning"
    healthcheck:
      test: [ "CMD", "redis-cli", "ping" ]
      interval: 5s
      timeout: 3s
      retries: 5
    restart: unless-stopped

  kimbal-logic-db:
    image: postgres:17
    container_name: kimbal-logic-db
    env_file: "src/.env"
    ports:
      - "${POSTGRES_PORT}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: [ "CMD", "pg_isready", "-U", "${POSTGRES_USER}", "-d", "${POSTGRES_DB}" ]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
volumes:
  postgres_data:
  redis_data:
