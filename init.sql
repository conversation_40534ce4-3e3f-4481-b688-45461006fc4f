-- Initialize lookup tables for <PERSON><PERSON> Logic

-- Create tables if they don't exist

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    first_name <PERSON><PERSON><PERSON><PERSON>(100),
    last_name <PERSON><PERSON><PERSON><PERSON>(100),
    email VARCHAR(255) UNIQUE NOT NULL,
    password TEXT NOT NULL,
    role VARCHAR(50) DEFAULT 'ENGINEER',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE
);

-- States table
CREATE TABLE IF NOT EXISTS states (
    id SERIAL PRIMARY KEY,
    name VARCHA<PERSON>(100) UNIQUE
);

-- Market Types table
CREATE TABLE IF NOT EXISTS market_types (
    id SERIAL PRIMARY KEY,
    market_type VARCHAR(50) UNIQUE NOT NULL
);

-- Renewable Energy Types table
CREATE TABLE IF NOT EXISTS renewable_energy_types (
    id SERIAL PRIMARY KEY,
    renewable_energy_type VARCHAR(50) UNIQUE NOT NULL
);

-- Settlement Types table
CREATE TABLE IF NOT EXISTS settlement_types (
    id SERIAL PRIMARY KEY,
    settlement_type VARCHAR(50) UNIQUE NOT NULL
);

-- Condition Types table
CREATE TABLE IF NOT EXISTS condition_types (
    id SERIAL PRIMARY KEY,
    condition VARCHAR(50) UNIQUE NOT NULL
);

-- Ratio Values table
CREATE TABLE IF NOT EXISTS ratio_values (
    id SERIAL PRIMARY KEY,
    ratio_value VARCHAR(255) UNIQUE NOT NULL
);

-- Carry Forwards table
CREATE TABLE IF NOT EXISTS carry_forwards (
    id SERIAL PRIMARY KEY,
    carry_forward VARCHAR(100) UNIQUE NOT NULL
);

-- Factors table (if needed for future use)
CREATE TABLE IF NOT EXISTS factors (
    id SERIAL PRIMARY KEY,
    factor VARCHAR(100) UNIQUE NOT NULL
);

-- Market Types
INSERT INTO market_types (market_type) VALUES
    ('DAM'),
    ('GDAM'),
    ('RTM'),
    ('GRTM')
ON CONFLICT (market_type) DO NOTHING;

-- Renewable Energy Types
INSERT INTO renewable_energy_types (renewable_energy_type) VALUES
    ('Solar'),
    ('Wind'),
    ('Battery')
ON CONFLICT (renewable_energy_type) DO NOTHING;

-- Condition Types
INSERT INTO condition_types (condition) VALUES
    ('Less Than'),
    ('Greater Than'),
    ('Equal To'),
    ('Not Equal To')
ON CONFLICT (condition) DO NOTHING;

-- Settlement Types (keeping existing ones)
INSERT INTO settlement_types (settlement_type) VALUES
    ('Daily'),
    ('Weekly'),
    ('Monthly')
ON CONFLICT (settlement_type) DO NOTHING;

-- Carry Forward Options
INSERT INTO carry_forwards (carry_forward) VALUES
    ('Yes'),
    ('No')
ON CONFLICT (carry_forward) DO NOTHING;

-- Ratio Values
INSERT INTO ratio_values (ratio_value) VALUES
    ('Predicted Demand'),
    ('Actual Demand'),
    ('Market Price'),
    ('Supply Capacity'),
    ('Predicted Demand X Bid Safety Factor'),
    ('Predicted Price X Price Safety Factor')
ON CONFLICT (ratio_value) DO NOTHING;

-- Factors
INSERT INTO factors (factor) VALUES
    ('Demand Safety Factor'),
    ('Price Factor'),
    ('Capacity Factor'),
    ('Efficiency Factor')
ON CONFLICT (factor) DO NOTHING;

-- Indian States
INSERT INTO states (name) VALUES
    ('Andaman and Nicobar Islands'),
    ('Andhra Pradesh'),
    ('Arunachal Pradesh'),
    ('Assam'),
    ('Bihar'),
    ('Chandigarh'),
    ('Chhattisgarh'),
    ('Dadra and Nagar Haveli and Daman and Diu'),
    ('Delhi'),
    ('Goa'),
    ('Gujarat'),
    ('Haryana'),
    ('Himachal Pradesh'),
    ('Jammu and Kashmir'),
    ('Jharkhand'),
    ('Karnataka'),
    ('Kerala'),
    ('Ladakh'),
    ('Lakshadweep'),
    ('Madhya Pradesh'),
    ('Maharashtra'),
    ('Manipur'),
    ('Meghalaya'),
    ('Mizoram'),
    ('Nagaland'),
    ('Odisha'),
    ('Puducherry'),
    ('Punjab'),
    ('Rajasthan'),
    ('Sikkim'),
    ('Tamil Nadu'),
    ('Telangana'),
    ('Tripura'),
    ('Uttar Pradesh'),
    ('Uttarakhand'),
    ('West Bengal')
ON CONFLICT (name) DO NOTHING;

-- Users
INSERT INTO users (id, email, first_name, last_name, password, role) VALUES
    (gen_random_uuid(), '<EMAIL>', 'user', 'example', '$2b$12$v9C2Wz6SarwbLz0Uft19J.VuoATFxfL9rkNSJ4kT.LxQJeGOZiYdy', 'ADMIN')
ON CONFLICT (email) DO NOTHING;
